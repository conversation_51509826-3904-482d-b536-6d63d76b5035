#!/usr/bin/env python3
"""
TradingEconomics Scraper for Forex Price Action Analysis System

This module scrapes real economic data from tradingeconomics.com for the seven major currency pairs.
It replaces the previous data generator with actual web scraping to provide accurate economic indicators.
"""

import os
import json
import time
import random
import logging
import requests
import hashlib
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import pandas as pd

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("tradingeconomics_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
CURRENCY_PAIRS = {
    'EUR/USD': {'base': 'euro-area', 'quote': 'united-states'},
    'USD/JPY': {'base': 'united-states', 'quote': 'japan'},
    'GBP/USD': {'base': 'united-kingdom', 'quote': 'united-states'},
    'USD/CHF': {'base': 'united-states', 'quote': 'switzerland'},
    'USD/CAD': {'base': 'united-states', 'quote': 'canada'},
    'AUD/USD': {'base': 'australia', 'quote': 'united-states'},
    'NZD/USD': {'base': 'new-zealand', 'quote': 'united-states'}
}

# List of indicators to scrape
INDICATORS = {
    'gdp_data': ['GDP Growth Rate', 'GDP Annual Growth Rate'],
    'employment_data': ['Unemployment Rate', 'Non Farm Payrolls'],
    'core_inflation': ['Core Inflation Rate'],
    'cpi_data': ['Inflation Rate', 'Inflation Rate MoM'],
    'interest_rates': ['Interest Rate'],
    'ppi_data': ['Producer Prices Change'],
    'pce_data': ['PCE Price Index'],
    'services_pmi': ['Services PMI'],
    'manufacturing_pmi': ['Manufacturing PMI'],
    'retail_sales': ['Retail Sales MoM', 'Retail Sales YoY'],
    'consumer_confidence': ['Consumer Confidence'],
    'business_forecast': ['Business Confidence'],
    'us_housing_data': ['Housing Starts', 'Building Permits', 'Home Sales'],
    'fear_greed_index': ['Stock Market'],  # Using stock market as proxy
    'retail_sentiment': ['Consumer Confidence'],  # Using consumer confidence as proxy
    'seasonality_data': [],  # Will be calculated based on historical data
    'cot_data': []  # Not directly available, will use proxy or placeholder
}

# Headers to mimic a browser
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Cache-Control': 'max-age=0'
}

# Cache settings
CACHE_DURATION = 3600  # Cache duration in seconds (1 hour)
CACHE_HISTORY_SIZE = 3  # Number of historical data points to keep

def get_base_path(app_type):
    """Get the base path for data storage based on app type"""
    if app_type == 'local':
        return '/home/<USER>/forex_web_app_local'
    elif app_type == 'deployable':
        return '/home/<USER>/forex_web_app_deployable'
    else:
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def get_cache_path(app_type):
    """Get the cache directory path"""
    base_path = get_base_path(app_type)
    cache_path = os.path.join(base_path, 'data', 'cache')
    os.makedirs(cache_path, exist_ok=True)
    return cache_path

def get_cache_file(country, app_type):
    """Get the cache file path for a specific country"""
    cache_path = get_cache_path(app_type)
    return os.path.join(cache_path, f"{country}_indicators.json")

def get_history_file(app_type):
    """Get the history file path"""
    cache_path = get_cache_path(app_type)
    return os.path.join(cache_path, "history.json")

def is_cache_valid(country, app_type):
    """Check if the cache for a country is still valid"""
    cache_file = get_cache_file(country, app_type)
    
    if not os.path.exists(cache_file):
        return False
    
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        # Check if cache has timestamp and is not expired
        if 'timestamp' in cache_data:
            cache_time = datetime.fromisoformat(cache_data['timestamp'])
            current_time = datetime.now()
            
            # Cache is valid if it's less than CACHE_DURATION seconds old
            return (current_time - cache_time).total_seconds() < CACHE_DURATION
    except (json.JSONDecodeError, ValueError, KeyError) as e:
        logger.warning(f"Error reading cache for {country}: {str(e)}")
    
    return False

def load_from_cache(country, app_type):
    """Load country indicators from cache"""
    cache_file = get_cache_file(country, app_type)
    
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        logger.info(f"Loaded {country} indicators from cache")
        return cache_data.get('indicators', {})
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.warning(f"Error loading cache for {country}: {str(e)}")
        return {}

def save_to_cache(country, indicators, app_type):
    """Save country indicators to cache"""
    cache_file = get_cache_file(country, app_type)
    
    try:
        cache_data = {
            'timestamp': datetime.now().isoformat(),
            'indicators': indicators
        }
        
        with open(cache_file, 'w') as f:
            json.dump(cache_data, f, indent=2)
        
        logger.info(f"Saved {country} indicators to cache")
        return True
    except Exception as e:
        logger.error(f"Error saving cache for {country}: {str(e)}")
        return False

def update_history(pair_analyses, app_type):
    """Update history with new data points"""
    history_file = get_history_file(app_type)
    
    try:
        # Load existing history
        if os.path.exists(history_file):
            with open(history_file, 'r') as f:
                history = json.load(f)
        else:
            history = {'currency_pairs': {}}
        
        # Get current timestamp
        timestamp = datetime.now().isoformat()
        
        # Update history for each pair
        for pair, analysis in pair_analyses.items():
            if pair not in history['currency_pairs']:
                history['currency_pairs'][pair] = []
            
            # Add new data point
            history['currency_pairs'][pair].append({
                'timestamp': timestamp,
                'score': analysis['score'],
                'signal': analysis['signal'],
                'confidence': analysis['confidence']
            })
            
            # Keep only the latest CACHE_HISTORY_SIZE data points
            history['currency_pairs'][pair] = history['currency_pairs'][pair][-CACHE_HISTORY_SIZE:]
        
        # Save updated history
        with open(history_file, 'w') as f:
            json.dump(history, f, indent=2)
        
        logger.info(f"Updated history with new data points")
        return True
    except Exception as e:
        logger.error(f"Error updating history: {str(e)}")
        return False

def get_history(pair, app_type):
    """Get history for a specific currency pair"""
    history_file = get_history_file(app_type)
    
    try:
        if os.path.exists(history_file):
            with open(history_file, 'r') as f:
                history = json.load(f)
            
            return history.get('currency_pairs', {}).get(pair, [])
        else:
            return []
    except Exception as e:
        logger.error(f"Error getting history for {pair}: {str(e)}")
        return []

def scrape_country_indicators(country, app_type):
    """
    Scrape economic indicators for a specific country
    
    Args:
        country (str): Country name as used in tradingeconomics.com URLs
        app_type (str): Application type ('local' or 'deployable')
        
    Returns:
        dict: Dictionary of indicators and their values
    """
    # Check if valid cache exists
    if is_cache_valid(country, app_type):
        logger.info(f"Using cached data for {country}")
        return load_from_cache(country, app_type)
    
    logger.info(f"Scraping indicators for {country}")
    url = f"https://tradingeconomics.com/{country}/indicators"
    
    try:
        # Add random delay to avoid rate limiting
        time.sleep(random.uniform(1, 3))
        
        # Make the request
        response = requests.get(url, headers=HEADERS)
        response.raise_for_status()
        
        # Parse the HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find the main table
        table = soup.find('table', {'class': 'table'})
        if not table:
            logger.warning(f"No indicator table found for {country}")
            return {}
        
        # Extract indicators
        indicators = {}
        rows = table.find_all('tr')
        
        for row in rows:
            cells = row.find_all('td')
            if not cells or len(cells) < 2:
                continue
            
            # Extract indicator name and value
            indicator_cell = cells[0]
            indicator_link = indicator_cell.find('a')
            if not indicator_link:
                continue
                
            indicator_name = indicator_link.text.strip()
            
            # Extract the latest value
            value_cell = cells[1]
            value = value_cell.text.strip() if value_cell else "N/A"
            
            # Extract previous value
            prev_value_cell = cells[2] if len(cells) > 2 else None
            prev_value = prev_value_cell.text.strip() if prev_value_cell else "N/A"
            
            # Extract unit if available
            unit_cell = cells[5] if len(cells) > 5 else None
            unit = unit_cell.text.strip() if unit_cell else ""
            
            # Extract date if available
            date_cell = cells[6] if len(cells) > 6 else None
            date = date_cell.text.strip() if date_cell else ""
            
            indicators[indicator_name] = {
                'value': value,
                'previous': prev_value,
                'unit': unit,
                'date': date
            }
        
        logger.info(f"Successfully scraped {len(indicators)} indicators for {country}")
        
        # Save to cache
        save_to_cache(country, indicators, app_type)
        
        return indicators
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error scraping {country}: {str(e)}")
        # Try to load from cache even if expired
        cached_data = load_from_cache(country, app_type)
        if cached_data:
            logger.info(f"Using expired cache for {country} due to scraping error")
            return cached_data
        return {}
    except Exception as e:
        logger.error(f"Unexpected error scraping {country}: {str(e)}")
        # Try to load from cache even if expired
        cached_data = load_from_cache(country, app_type)
        if cached_data:
            logger.info(f"Using expired cache for {country} due to scraping error")
            return cached_data
        return {}

def scrape_all_countries(app_type):
    """
    Scrape indicators for all countries needed for the major currency pairs
    
    Args:
        app_type (str): Application type ('local' or 'deployable')
        
    Returns:
        dict: Dictionary of countries and their indicators
    """
    logger.info("Starting to scrape all countries")
    countries = set()
    for pair_data in CURRENCY_PAIRS.values():
        countries.add(pair_data['base'])
        countries.add(pair_data['quote'])
    
    all_country_data = {}
    for country in countries:
        all_country_data[country] = scrape_country_indicators(country, app_type)
        # Add delay between countries to avoid rate limiting
        time.sleep(random.uniform(2, 5))
    
    logger.info(f"Completed scraping {len(all_country_data)} countries")
    return all_country_data

def calculate_indicator_differential(base_country, quote_country, indicator, country_data):
    """
    Calculate the differential between two countries for a specific indicator
    
    Args:
        base_country (str): Base currency country
        quote_country (str): Quote currency country
        indicator (str): Indicator name
        country_data (dict): Dictionary of country data
        
    Returns:
        float: Differential value or None if data is missing
    """
    try:
        if base_country not in country_data or quote_country not in country_data:
            return None
            
        base_indicators = country_data[base_country]
        quote_indicators = country_data[quote_country]
        
        if indicator not in base_indicators or indicator not in quote_indicators:
            return None
            
        base_value = float(base_indicators[indicator]['value'].replace(',', ''))
        quote_value = float(quote_indicators[indicator]['value'].replace(',', ''))
        
        return base_value - quote_value
    except (ValueError, KeyError) as e:
        logger.warning(f"Error calculating differential for {indicator}: {str(e)}")
        return None

def generate_pair_analysis(pair, country_data):
    """
    Generate analysis for a specific currency pair
    
    Args:
        pair (str): Currency pair (e.g., 'EUR/USD')
        country_data (dict): Dictionary of country data
        
    Returns:
        dict: Analysis data for the pair
    """
    logger.info(f"Generating analysis for {pair}")
    
    if pair not in CURRENCY_PAIRS:
        logger.warning(f"Unknown currency pair: {pair}")
        return {}
    
    base_country = CURRENCY_PAIRS[pair]['base']
    quote_country = CURRENCY_PAIRS[pair]['quote']
    
    # Calculate differentials for key indicators
    differentials = {}
    for indicator in ['Interest Rate', 'Inflation Rate', 'GDP Growth Rate', 'Unemployment Rate']:
        diff = calculate_indicator_differential(base_country, quote_country, indicator, country_data)
        if diff is not None:
            differentials[f"{indicator} Differential"] = diff
    
    # Extract other indicators
    indicators = {}
    for country, country_code in [(base_country, 'base'), (quote_country, 'quote')]:
        if country in country_data:
            for indicator, data in country_data[country].items():
                indicators[f"{indicator} ({country_code})"] = data
    
    # Calculate sentiment score based on differentials
    score = 0
    confidence = 0
    
    # Interest rate differential (higher base rate = stronger currency)
    if 'Interest Rate Differential' in differentials:
        score += differentials['Interest Rate Differential'] * 0.4
        confidence += 0.2
    
    # Inflation differential (lower base inflation = stronger currency)
    if 'Inflation Rate Differential' in differentials:
        score -= differentials['Inflation Rate Differential'] * 0.3
        confidence += 0.2
    
    # Growth differential (higher base growth = stronger currency)
    if 'GDP Growth Rate Differential' in differentials:
        score += differentials['GDP Growth Rate Differential'] * 0.2
        confidence += 0.2
    
    # Unemployment differential (lower base unemployment = stronger currency)
    if 'Unemployment Rate Differential' in differentials:
        score -= differentials['Unemployment Rate Differential'] * 0.1
        confidence += 0.2
    
    # Normalize score to range between -2 and 2
    if confidence > 0:
        score = max(min(score, 2), -2)
        confidence = min(confidence, 1) * 100  # Convert to percentage
    else:
        score = 0
        confidence = 50  # Default confidence
    
    # Determine signal based on score
    signal = "Neutral"
    if score > 1:
        signal = "Strong Buy"
    elif score > 0.3:
        signal = "Buy"
    elif score < -1:
        signal = "Strong Sell"
    elif score < -0.3:
        signal = "Sell"
    
    # Determine optimal horizon based on score magnitude
    optimal_horizon = 15  # Default
    score_magnitude = abs(score)
    if score_magnitude > 1.5:
        optimal_horizon = 3  # Short-term for strong signals
    elif score_magnitude > 0.8:
        optimal_horizon = 7  # Medium-term for moderate signals
    elif score_magnitude > 0.3:
        optimal_horizon = 15  # Longer-term for weak signals
    
    analysis = {
        'pair': pair,
        'score': round(score, 2),
        'signal': signal,
        'confidence': round(confidence),
        'optimal_horizon': optimal_horizon,
        'differentials': differentials,
        'indicators': indicators
    }
    
    logger.info(f"Analysis for {pair}: Score={score}, Signal={signal}, Confidence={confidence}%")
    return analysis

def generate_top_indicators(pair_analysis):
    """
    Generate top indicators based on pair analysis
    
    Args:
        pair_analysis (dict): Analysis data for a currency pair
        
    Returns:
        list: List of top indicators with their impact scores
    """
    # Define indicator weights
    weights = {
        'Interest Rate Differential': 0.9,
        'Inflation Rate Differential': 0.8,
        'GDP Growth Rate Differential': 0.7,
        'Unemployment Rate Differential': 0.7,
        'Manufacturing PMI (base)': 0.6,
        'Services PMI (base)': 0.6,
        'Consumer Confidence (base)': 0.5,
        'Retail Sales MoM (base)': 0.5,
        'Business Confidence (base)': 0.4
    }
    
    # Generate impact scores for indicators
    top_indicators = []
    
    # Add differentials with their actual values
    for indicator, value in pair_analysis.get('differentials', {}).items():
        weight = weights.get(indicator, 0.5)
        # Normalize the value to a score between -1 and 1
        normalized_value = max(min(value / 2, 1), -1)
        impact = normalized_value * weight
        top_indicators.append({
            'name': indicator.replace(' Differential', ''),
            'impact': round(impact, 2)
        })
    
    # Add other indicators with estimated impact based on their values
    for indicator_name, weight in weights.items():
        if indicator_name not in pair_analysis.get('differentials', {}):
            # Skip if already added as differential
            clean_name = indicator_name.replace(' (base)', '').replace(' (quote)', '')
            if any(i['name'] == clean_name for i in top_indicators):
                continue
            
            # Look for the indicator in the indicators dictionary
            base_indicator = None
            for key, value in pair_analysis.get('indicators', {}).items():
                if clean_name in key and '(base)' in key:
                    try:
                        base_indicator = float(value['value'].replace(',', ''))
                        break
                    except (ValueError, KeyError):
                        pass
            
            # If we found a value, use it to calculate impact
            if base_indicator is not None:
                # Normalize to a reasonable range based on the indicator type
                if 'PMI' in clean_name:
                    # PMI above 50 is positive, below is negative
                    normalized_value = (base_indicator - 50) / 10
                elif 'Confidence' in clean_name:
                    # Normalize confidence to -1 to 1 range
                    normalized_value = base_indicator / 100
                else:
                    # Generic normalization
                    normalized_value = base_indicator / 5
                
                # Clamp to -1 to 1 range
                normalized_value = max(min(normalized_value, 1), -1)
                impact = normalized_value * weight
            else:
                # If no value found, use a small random value
                impact = (random.random() * 0.4 + 0.3) * (1 if random.random() > 0.5 else -1)
            
            top_indicators.append({
                'name': clean_name,
                'impact': round(impact, 2)
            })
    
    # Sort by absolute impact and take top 10
    top_indicators.sort(key=lambda x: abs(x['impact']), reverse=True)
    return top_indicators[:10]

def save_to_csv(data, filename, app_type):
    """
    Save data to CSV file
    
    Args:
        data (list): List of dictionaries to save
        filename (str): Filename to save to
        app_type (str): Application type ('local' or 'deployable')
    """
    base_path = get_base_path(app_type)
    directory = os.path.join(base_path, 'data', 'economic_indicators')
    
    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)
    
    file_path = os.path.join(directory, filename)
    
    try:
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False)
        logger.info(f"Saved {filename} to {file_path}")
    except Exception as e:
        logger.error(f"Error saving {filename}: {str(e)}")

def create_dashboard_data(pair_analyses, app_type):
    """
    Create consolidated dashboard data from pair analyses
    
    Args:
        pair_analyses (dict): Dictionary of pair analyses
        app_type (str): Application type ('local' or 'deployable')
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Creating consolidated dashboard data...")
    
    dashboard_data = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'currency_pairs': {}
    }
    
    for pair, analysis in pair_analyses.items():
        top_indicators = generate_top_indicators(analysis)
        history = get_history(pair, app_type)
        
        dashboard_data['currency_pairs'][pair] = {
            'score': analysis['score'],
            'signal': analysis['signal'],
            'confidence': analysis['confidence'],
            'optimal_horizon': analysis['optimal_horizon'],
            'top_indicators': top_indicators,
            'history': history
        }
    
    # Save to JSON file
    base_path = get_base_path(app_type)
    file_path = os.path.join(base_path, 'data', 'dashboard_data.json')
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    try:
        with open(file_path, 'w') as f:
            json.dump(dashboard_data, f, indent=2)
        logger.info(f"Saved consolidated dashboard data to {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving dashboard data: {str(e)}")
        return False

def run_scraper(app_type='local'):
    """
    Main function to run the scraper
    
    Args:
        app_type (str): Application type ('local' or 'deployable')
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Starting Trading Economics scraper for Forex Price Action Analysis System")
    
    try:
        # Scrape data for all countries
        country_data = scrape_all_countries(app_type)
        
        # Generate analysis for each currency pair
        pair_analyses = {}
        for pair in CURRENCY_PAIRS:
            pair_analyses[pair] = generate_pair_analysis(pair, country_data)
        
        # Update history with new data points
        update_history(pair_analyses, app_type)
        
        # Create consolidated dashboard data
        success = create_dashboard_data(pair_analyses, app_type)
        
        if success:
            logger.info("Trading Economics scraper completed successfully")
            return True
        else:
            logger.error("Failed to create dashboard data")
            return False
            
    except Exception as e:
        logger.error(f"Error in Trading Economics scraper: {str(e)}")
        return False

if __name__ == "__main__":
    run_scraper()
