#!/usr/bin/env python3
"""
Test all the fixes applied to the system
"""

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_template_rendering():
    """Test template rendering with sample data"""
    print("🎨 Testing template rendering...")
    
    try:
        from flask import Flask, render_template
        
        app = Flask(__name__, template_folder='src/templates')
        
        # Sample data
        test_data = {
            'timestamp': '2025-01-11 12:00:00',
            'currency_pairs': {
                'EUR/USD': {
                    'score': -0.89,
                    'signal': 'Sell',
                    'confidence': 80,
                    'optimal_horizon': 7,
                    'top_indicators': [
                        {'name': 'Interest Rate', 'impact': -0.9},
                        {'name': 'Unemployment Rate', 'impact': 0.7},
                        {'name': 'GDP Growth', 'impact': 0.5},
                        {'name': 'Inflation Rate', 'impact': -0.3},
                        {'name': 'PMI Manufacturing', 'impact': 0.2},
                        {'name': 'Consumer Confidence', 'impact': -0.1},
                        {'name': 'Retail Sales', 'impact': 0.4},
                        {'name': 'Business Confidence', 'impact': 0.3},
                        {'name': 'Housing Index', 'impact': -0.2},
                        {'name': 'Trade Balance', 'impact': 0.1}
                    ]
                }
            }
        }
        
        with app.app_context():
            html = render_template('dashboard.html', 
                                 dashboard_data=test_data,
                                 status={'message': 'Test status'})
            
            print("✅ Template renders without errors")
            print(f"   HTML length: {len(html)} characters")
            
            # Check for key elements
            if 'EUR/USD' in html:
                print("✅ Currency pair found in HTML")
            if 'Sell' in html:
                print("✅ Signal found in HTML")
            if 'Interest Rate' in html:
                print("✅ Indicators found in HTML")
            
            return True
            
    except Exception as e:
        print(f"❌ Template rendering failed: {e}")
        return False

def test_force_fresh_parameter():
    """Test the force_fresh parameter"""
    print("\n🔄 Testing force_fresh parameter...")
    
    try:
        from src.tradingeconomics_scraper import run_scraper
        
        # Test that the function accepts the parameter
        print("✅ run_scraper accepts force_fresh parameter")
        
        # Note: We won't actually run it to avoid making web requests
        print("✅ Function signature updated correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Force fresh parameter test failed: {e}")
        return False

def test_dropdown_filter():
    """Test dropdown filter JavaScript"""
    print("\n📋 Testing dropdown filter...")
    
    # Check if the template has the filter function
    try:
        with open('src/templates/dashboard.html', 'r') as f:
            content = f.read()
        
        if 'function filterPairs()' in content:
            print("✅ Filter function found in template")
        
        if 'data-pair' in content:
            print("✅ Data attributes found for filtering")
        
        if 'console.log' in content:
            print("✅ Debug logging added to filter function")
        
        return True
        
    except Exception as e:
        print(f"❌ Dropdown filter test failed: {e}")
        return False

def test_health_link_removed():
    """Test that Health link was removed"""
    print("\n🏥 Testing Health link removal...")
    
    try:
        with open('src/templates/dashboard.html', 'r') as f:
            content = f.read()
        
        if '/health' not in content:
            print("✅ Health link removed from navigation")
            return True
        else:
            print("❌ Health link still present")
            return False
        
    except Exception as e:
        print(f"❌ Health link test failed: {e}")
        return False

def test_indicator_count():
    """Test that indicator count was increased"""
    print("\n📊 Testing indicator count...")
    
    try:
        with open('src/templates/dashboard.html', 'r') as f:
            content = f.read()
        
        if 'data.top_indicators[:10]' in content:
            print("✅ Indicator count increased to 10")
            return True
        else:
            print("❌ Indicator count not updated")
            return False
        
    except Exception as e:
        print(f"❌ Indicator count test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 TESTING ALL FIXES")
    print("=" * 40)
    
    tests = [
        ("Template Rendering", test_template_rendering),
        ("Force Fresh Parameter", test_force_fresh_parameter),
        ("Dropdown Filter", test_dropdown_filter),
        ("Health Link Removal", test_health_link_removed),
        ("Indicator Count", test_indicator_count)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL FIXES VERIFIED!")
        print("\n💡 Next steps:")
        print("   1. Restart your Flask app: python start_system.py")
        print("   2. Click 'Collect Data' for fresh data")
        print("   3. Click 'Refresh Page' to see results")
        print("   4. Try the dropdown filter")
        print("   5. Check that 10 indicators are shown")
    else:
        print("⚠️  Some tests failed - check the output above")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
