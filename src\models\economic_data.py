"""
Database models for economic data storage and history tracking
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class EconomicIndicator(db.Model):
    """Model for storing economic indicator data with history"""
    id = db.Column(db.Integer, primary_key=True)
    indicator_type = db.Column(db.String(50), nullable=False)
    currency = db.Column(db.String(10), nullable=False)
    value = db.Column(db.Float, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<EconomicIndicator {self.indicator_type} {self.currency}: {self.value}>"
    
    @classmethod
    def get_latest_for_currency(cls, indicator_type, currency, limit=3):
        """Get the latest records for a specific indicator and currency"""
        return cls.query.filter_by(
            indicator_type=indicator_type, 
            currency=currency
        ).order_by(
            cls.timestamp.desc()
        ).limit(limit).all()

class DashboardData(db.Model):
    """Model for storing dashboard data snapshots"""
    id = db.Column(db.Integer, primary_key=True)
    pair = db.Column(db.String(10), nullable=False)
    score = db.Column(db.Float, nullable=False)
    signal = db.Column(db.String(20), nullable=False)
    confidence = db.Column(db.Float, nullable=False)
    optimal_horizon = db.Column(db.Integer, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f"<DashboardData {self.pair}: {self.score} ({self.signal})>"
    
    @classmethod
    def get_latest_for_pair(cls, pair, limit=3):
        """Get the latest dashboard data for a specific currency pair"""
        return cls.query.filter_by(
            pair=pair
        ).order_by(
            cls.timestamp.desc()
        ).limit(limit).all()
