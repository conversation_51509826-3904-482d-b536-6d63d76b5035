#!/usr/bin/env python3
"""
Main entry point for the Forex Price Action Analysis System

This script coordinates the data collection, analysis, and dashboard update process.
"""

import os
import sys
import logging
import subprocess
import json
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("main.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_base_path():
    """Get the base path for the application"""
    return os.path.dirname(os.path.abspath(__file__))

def run_scraper():
    """Run the Trading Economics scraper"""
    logger.info("Running Trading Economics scraper...")

    try:
        from tradingeconomics_scraper import run_scraper
        success = run_scraper()

        if success:
            logger.info("Trading Economics scraper completed successfully")
            return True
        else:
            logger.error("Trading Economics scraper failed")
            return False
    except Exception as e:
        logger.error(f"Error running Trading Economics scraper: {str(e)}")
        return False

def update_dashboard():
    """Update the dashboard with the latest data"""
    logger.info("Dashboard update skipped - using Flask web interface")

    # In the new Flask web application, the dashboard is rendered dynamically
    # No need to generate static HTML files
    return True

def copy_dashboard_to_root():
    """Copy dashboard to root - not needed in Flask web application"""
    logger.info("Dashboard copy skipped - using Flask web interface")

    # In the new Flask web application, users access the dashboard via web browser
    # No need to copy static files
    return True

def export_data_to_csv():
    """Export the economic indicators data to CSV files"""
    logger.info("Exporting data to CSV files...")

    try:
        base_path = get_base_path()
        data_path = os.path.join(base_path, '..', 'data', 'dashboard_data.json')
        export_dir = os.path.join(base_path, '..', 'data', 'exports')

        # Create export directory if it doesn't exist
        os.makedirs(export_dir, exist_ok=True)

        # Load dashboard data
        with open(data_path, 'r') as f:
            dashboard_data = json.load(f)

        # Export data for each currency pair
        for pair, data in dashboard_data.get('currency_pairs', {}).items():
            # Export top indicators
            indicators = data.get('top_indicators', [])
            if indicators:
                indicators_file = os.path.join(export_dir, f"{pair}_indicators.csv")
                with open(indicators_file, 'w') as f:
                    f.write("Indicator,Impact\n")
                    for indicator in indicators:
                        f.write(f"{indicator.get('name', 'N/A')},{indicator.get('impact', 0)}\n")

            # Export history
            history = data.get('history', [])
            if history:
                history_file = os.path.join(export_dir, f"{pair}_history.csv")
                with open(history_file, 'w') as f:
                    f.write("Date,Score,Signal,Confidence\n")
                    for entry in history:
                        date = entry.get('timestamp', 'N/A')
                        score = entry.get('score', 'N/A')
                        signal = entry.get('signal', 'N/A')
                        confidence = entry.get('confidence', 'N/A')
                        f.write(f"{date},{score},{signal},{confidence}\n")

        logger.info(f"Data exported to {export_dir}")
        return True
    except Exception as e:
        logger.error(f"Error exporting data: {str(e)}")
        return False

def main():
    """Main function to run the Forex Price Action Analysis System"""
    logger.info("Starting Forex Price Action Analysis System")

    # Run the Trading Economics scraper
    if not run_scraper():
        logger.error("Failed to run Trading Economics scraper")
        return False

    # Update the dashboard
    if not update_dashboard():
        logger.error("Failed to update dashboard")
        return False

    # Copy the dashboard HTML to the root directory
    if not copy_dashboard_to_root():
        logger.error("Failed to copy dashboard HTML to root directory")
        # Continue anyway, not critical

    # Export data to CSV files
    if not export_data_to_csv():
        logger.error("Failed to export data to CSV files")
        # Continue anyway, not critical

    logger.info("Forex Price Action Analysis System completed successfully")
    return True

if __name__ == "__main__":
    main()
