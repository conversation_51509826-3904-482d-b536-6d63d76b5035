#!/usr/bin/env python3
"""
Dashboard Updater for Forex Price Action Analysis System

This module updates the dashboard HTML with the latest economic data from the scraper.
"""

import os
import json
import logging
from datetime import datetime
import re

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("dashboard_updater.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_base_path(app_type='local'):
    """Get the base path for data storage based on app type"""
    # Always use the current working directory structure
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def load_dashboard_data(app_type):
    """
    Load dashboard data from JSON file

    Args:
        app_type (str): Application type ('local' or 'deployable')

    Returns:
        dict: Dashboard data
    """
    base_path = get_base_path(app_type)
    file_path = os.path.join(base_path, 'data', 'dashboard_data.json')

    try:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                data = json.load(f)
            logger.info(f"Loaded dashboard data from {file_path}")
            return data
        else:
            logger.warning(f"Dashboard data file not found: {file_path}")
            return None
    except Exception as e:
        logger.error(f"Error loading dashboard data: {str(e)}")
        return None

def format_date(date_str):
    """Format date string for display"""
    try:
        dt = datetime.fromisoformat(date_str)
        return dt.strftime('%Y-%m-%d %H:%M')
    except:
        return date_str

def generate_history_html(history):
    """Generate HTML for history data"""
    if not history or len(history) == 0:
        return "<p>No historical data available</p>"

    html = "<table class='history-table'>"
    html += "<tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr>"

    for entry in reversed(history):
        date = format_date(entry.get('timestamp', 'N/A'))
        score = entry.get('score', 'N/A')
        signal = entry.get('signal', 'N/A')
        confidence = entry.get('confidence', 'N/A')

        html += f"<tr><td>{date}</td><td>{score}</td><td>{signal}</td><td>{confidence}%</td></tr>"

    html += "</table>"
    return html

def generate_indicators_html(indicators):
    """Generate HTML for top indicators"""
    if not indicators or len(indicators) == 0:
        return "<p>No indicator data available</p>"

    html = "<table class='indicators-table'>"
    html += "<tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr>"

    for indicator in indicators:
        name = indicator.get('name', 'N/A')
        impact = indicator.get('impact', 0)

        # Generate bar visualization
        bar_width = abs(impact) * 50  # Scale to reasonable width
        bar_color = "green" if impact > 0 else "red"
        bar_direction = "right" if impact > 0 else "left"

        bar_html = f"<div class='impact-bar-container'>"
        if bar_direction == "left":
            bar_html += f"<div class='impact-bar impact-negative' style='width: {bar_width}px;'></div>"
        else:
            bar_html += f"<div class='impact-bar impact-positive' style='width: {bar_width}px;'></div>"
        bar_html += "</div>"

        html += f"<tr><td>{name}</td><td>{impact}</td><td>{bar_html}</td></tr>"

    html += "</table>"
    return html

def generate_pair_html(pair, data):
    """Generate HTML for a currency pair"""
    score = data.get('score', 'N/A')
    signal = data.get('signal', 'Neutral')
    confidence = data.get('confidence', 'N/A')
    optimal_horizon = data.get('optimal_horizon', 'N/A')
    top_indicators = data.get('top_indicators', [])
    history = data.get('history', [])

    # Determine signal class for styling
    signal_class = "neutral"
    if signal == "Strong Buy":
        signal_class = "strong-buy"
    elif signal == "Buy":
        signal_class = "buy"
    elif signal == "Strong Sell":
        signal_class = "strong-sell"
    elif signal == "Sell":
        signal_class = "sell"

    html = f"""
    <div class="pair-card">
        <div class="pair-header">
            <h2>{pair}</h2>
            <div class="signal-badge {signal_class}">{signal}</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">{score}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">{confidence}%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">{optimal_horizon} days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            {generate_indicators_html(top_indicators)}
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            {generate_history_html(history)}
        </div>
    </div>
    """

    return html

def update_dashboard_html(app_type):
    """
    Update the dashboard HTML with the latest data

    Args:
        app_type (str): Application type ('local' or 'deployable')

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Updating dashboard HTML for {app_type}")

    # Load dashboard data
    dashboard_data = load_dashboard_data(app_type)
    if not dashboard_data:
        logger.error("Failed to load dashboard data")
        return False

    # Get template path - use the correct template name
    base_path = get_base_path(app_type)
    template_path = os.path.join(base_path, 'src', 'templates', 'dashboard.html')
    output_path = os.path.join(base_path, 'data', 'dashboard.html')

    # Check if template exists
    if not os.path.exists(template_path):
        logger.error(f"Template file not found: {template_path}")
        # Try alternative template name
        alt_template_path = os.path.join(base_path, 'src', 'templates', 'index.html')
        if os.path.exists(alt_template_path):
            template_path = alt_template_path
            logger.info(f"Using alternative template: {template_path}")
        else:
            logger.error(f"No template file found at {template_path} or {alt_template_path}")
            return False

    try:
        # Read template
        with open(template_path, 'r') as f:
            template = f.read()

        # Generate HTML for each currency pair
        pairs_html = ""
        for pair, data in dashboard_data.get('currency_pairs', {}).items():
            pairs_html += generate_pair_html(pair, data)

        # Update timestamp
        timestamp = dashboard_data.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # Replace placeholders in template
        template = template.replace('{{CURRENCY_PAIRS}}', pairs_html)
        template = template.replace('{{LAST_UPDATED}}', timestamp)

        # Write output
        with open(output_path, 'w') as f:
            f.write(template)

        logger.info(f"Dashboard HTML updated successfully: {output_path}")
        return True
    except Exception as e:
        logger.error(f"Error updating dashboard HTML: {str(e)}")
        return False

def run_updater(app_type='local'):
    """
    Main function to run the dashboard updater

    Args:
        app_type (str): Application type ('local' or 'deployable')

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Starting dashboard updater for Forex Price Action Analysis System")

    try:
        success = update_dashboard_html(app_type)

        if success:
            logger.info("Dashboard updater completed successfully")
            return True
        else:
            logger.error("Failed to update dashboard")
            return False

    except Exception as e:
        logger.error(f"Error in dashboard updater: {str(e)}")
        return False

if __name__ == "__main__":
    run_updater()
