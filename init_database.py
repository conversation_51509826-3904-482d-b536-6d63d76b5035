#!/usr/bin/env python3
"""
Initialize the database for the Forex system
"""

import os
import sqlite3

def create_database():
    """Create the SQLite database and tables"""
    
    # Ensure instance directory exists
    os.makedirs('instance', exist_ok=True)
    
    db_path = os.path.join('instance', 'forex_data.db')
    
    print(f"Creating database at: {db_path}")
    
    try:
        # Create database connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create economic_indicator table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS economic_indicator (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_type VARCHAR(50) NOT NULL,
                currency VARCHAR(10) NOT NULL,
                value FLOAT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create dashboard_data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dashboard_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pair VARCHAR(10) NOT NULL,
                score FLOAT NOT NULL,
                signal VARCHAR(20) NOT NULL,
                confidence FLOAT NOT NULL,
                optimal_horizon INTEGER NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_economic_indicator_currency 
            ON economic_indicator(currency)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_economic_indicator_timestamp 
            ON economic_indicator(timestamp)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_dashboard_data_pair 
            ON dashboard_data(pair)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_dashboard_data_timestamp 
            ON dashboard_data(timestamp)
        ''')
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("✅ Database created successfully!")
        print(f"   Location: {os.path.abspath(db_path)}")
        print(f"   Size: {os.path.getsize(db_path)} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        return False

def test_database():
    """Test database connection"""
    db_path = os.path.join('instance', 'forex_data.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("📊 Database tables:")
        for table in tables:
            print(f"   - {table[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def main():
    print("🗄️ FOREX DATABASE INITIALIZATION")
    print("=" * 40)
    
    if create_database():
        if test_database():
            print("\n🎉 Database initialization complete!")
            print("✅ Ready to run the Forex system")
        else:
            print("\n⚠️ Database created but test failed")
    else:
        print("\n❌ Database initialization failed")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
