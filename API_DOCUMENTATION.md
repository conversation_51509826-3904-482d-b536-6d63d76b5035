# Forex System Final V2 - API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
Currently, no authentication is required for API endpoints.

## Endpoints

### 1. Data Collection

#### POST /api/collect-data
Triggers the complete data collection process including scraping, analysis, and storage.

**Request:**
```http
POST /api/collect-data
Content-Type: application/json
```

**Response (Success):**
```json
{
    "success": true,
    "message": "Data collection completed successfully",
    "timestamp": "2025-01-11T10:30:00.123456"
}
```

**Response (Error):**
```json
{
    "success": false,
    "message": "Data collection failed"
}
```

**Status Codes:**
- `200 OK` - Data collection successful
- `500 Internal Server Error` - Collection failed

---

### 2. Dashboard Data

#### GET /api/dashboard-data
Retrieves the latest dashboard data including all currency pairs and their analysis.

**Request:**
```http
GET /api/dashboard-data
```

**Response:**
```json
{
    "timestamp": "2025-01-11 10:30:00",
    "currency_pairs": {
        "EUR/USD": {
            "score": 0.75,
            "signal": "Buy",
            "confidence": 85,
            "optimal_horizon": 7,
            "top_indicators": [
                {
                    "name": "Interest Rate",
                    "impact": 0.68
                },
                {
                    "name": "Inflation Rate",
                    "impact": -0.45
                }
            ],
            "history": [
                {
                    "score": 0.75,
                    "signal": "Buy",
                    "confidence": 85,
                    "timestamp": "2025-01-11T10:30:00.123456"
                }
            ]
        }
    }
}
```

**Status Codes:**
- `200 OK` - Data retrieved successfully
- `404 Not Found` - Dashboard data not available
- `500 Internal Server Error` - Server error

---

### 3. Indicator History

#### GET /api/indicator-history/{indicator_type}/{currency}
Retrieves historical data for a specific economic indicator and currency.

**Parameters:**
- `indicator_type` (string) - Type of indicator (e.g., "gdp_data", "interest_rates")
- `currency` (string) - Currency code (e.g., "USD", "EUR")

**Request:**
```http
GET /api/indicator-history/interest_rates/USD
```

**Response:**
```json
{
    "success": true,
    "history": [
        {
            "value": 5.25,
            "timestamp": "2025-01-11T10:30:00.123456"
        },
        {
            "value": 5.00,
            "timestamp": "2025-01-10T10:30:00.123456"
        },
        {
            "value": 4.75,
            "timestamp": "2025-01-09T10:30:00.123456"
        }
    ]
}
```

**Status Codes:**
- `200 OK` - History retrieved successfully
- `500 Internal Server Error` - Server error

---

### 4. CSV Export (Single Indicator)

#### GET /api/export-csv/{indicator_type}
Exports data for a specific indicator as a CSV file.

**Parameters:**
- `indicator_type` (string) - Type of indicator to export

**Request:**
```http
GET /api/export-csv/gdp_data
```

**Response:**
- **Content-Type:** `text/csv`
- **Content-Disposition:** `attachment; filename="gdp_data_20250111.csv"`

**CSV Format:**
```csv
currency,value,timestamp
USD,2.1,2025-01-11T10:30:00.123456
EUR,1.8,2025-01-11T10:30:00.123456
GBP,1.5,2025-01-11T10:30:00.123456
```

**Status Codes:**
- `200 OK` - CSV file generated successfully
- `404 Not Found` - No data found for indicator
- `500 Internal Server Error` - Export failed

---

### 5. CSV Export (All Data)

#### GET /api/export-all-csv
Exports all indicator data as a ZIP archive containing multiple CSV files.

**Request:**
```http
GET /api/export-all-csv
```

**Response:**
- **Content-Type:** `application/zip`
- **Content-Disposition:** `attachment; filename="forex_economic_data_20250111.zip"`

**ZIP Contents:**
```
forex_economic_data_20250111.zip
├── gdp_data.csv
├── interest_rates.csv
├── inflation_data.csv
├── unemployment_data.csv
└── ... (additional indicator files)
```

**Status Codes:**
- `200 OK` - ZIP file generated successfully
- `500 Internal Server Error` - Export failed

---

## Data Models

### Currency Pair Data
```json
{
    "score": 0.75,              // Sentiment score (-2 to +2)
    "signal": "Buy",            // Trading signal
    "confidence": 85,           // Confidence percentage (0-100)
    "optimal_horizon": 7,       // Optimal trading horizon in days
    "top_indicators": [...],    // Array of top indicators
    "history": [...]            // Historical data points
}
```

### Indicator Data
```json
{
    "name": "Interest Rate",    // Indicator name
    "impact": 0.68             // Impact score (-1 to +1)
}
```

### Historical Entry
```json
{
    "score": 0.75,                           // Sentiment score
    "signal": "Buy",                         // Trading signal
    "confidence": 85,                        // Confidence level
    "timestamp": "2025-01-11T10:30:00.123456" // ISO format timestamp
}
```

## Error Handling

### Standard Error Response
```json
{
    "success": false,
    "message": "Error description"
}
```

### Common Error Messages
- `"Data collection failed"` - Scraping or processing error
- `"Dashboard data not found"` - No dashboard data available
- `"No data found for indicator: {type}"` - Indicator not found
- `"Error retrieving dashboard data: {details}"` - Database error
- `"Error exporting CSV: {details}"` - Export process error

## Rate Limiting

### Current Limits
- No explicit rate limiting implemented
- Natural rate limiting through scraping delays (1-5 seconds)
- Caching reduces actual data collection frequency

### Recommendations
- Implement rate limiting for production use
- Consider API key authentication for access control
- Monitor usage patterns for optimization

## Usage Examples

### Python Client Example
```python
import requests
import json

# Trigger data collection
response = requests.post('http://localhost:5000/api/collect-data')
if response.status_code == 200:
    print("Data collection started")

# Get dashboard data
response = requests.get('http://localhost:5000/api/dashboard-data')
if response.status_code == 200:
    data = response.json()
    for pair, info in data['currency_pairs'].items():
        print(f"{pair}: {info['signal']} (Score: {info['score']})")

# Export specific indicator
response = requests.get('http://localhost:5000/api/export-csv/interest_rates')
if response.status_code == 200:
    with open('interest_rates.csv', 'wb') as f:
        f.write(response.content)
```

### JavaScript Client Example
```javascript
// Trigger data collection
fetch('/api/collect-data', { method: 'POST' })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Data collection completed');
        }
    });

// Get dashboard data
fetch('/api/dashboard-data')
    .then(response => response.json())
    .then(data => {
        Object.entries(data.currency_pairs).forEach(([pair, info]) => {
            console.log(`${pair}: ${info.signal} (Score: ${info.score})`);
        });
    });

// Export data as CSV
function exportCSV(indicator) {
    const link = document.createElement('a');
    link.href = `/api/export-csv/${indicator}`;
    link.download = `${indicator}.csv`;
    link.click();
}
```

### cURL Examples
```bash
# Trigger data collection
curl -X POST http://localhost:5000/api/collect-data

# Get dashboard data
curl http://localhost:5000/api/dashboard-data

# Get indicator history
curl http://localhost:5000/api/indicator-history/interest_rates/USD

# Export CSV
curl -O -J http://localhost:5000/api/export-csv/gdp_data

# Export all data
curl -O -J http://localhost:5000/api/export-all-csv
```

## Integration Notes

### Frontend Integration
- Dashboard template includes JavaScript for API interaction
- Real-time data updates through API calls
- Export functionality built into web interface

### Database Integration
- All API operations automatically update database
- Historical data maintained for trend analysis
- Efficient querying with proper indexing

### Monitoring Integration
- All API calls logged with timestamps
- Error tracking for debugging
- Performance metrics available in logs

## Future Enhancements

### Planned Features
- WebSocket support for real-time updates
- Authentication and authorization
- Rate limiting and quota management
- API versioning
- Pagination for large datasets
- Filtering and sorting parameters
- Webhook notifications for data updates
