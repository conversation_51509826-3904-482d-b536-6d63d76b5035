<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forex Analysis Dashboard - Standalone</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #212529;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .status-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #ffc107; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-ready { background-color: #6c757d; }

        .pair-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.2s;
        }
        .pair-card:hover {
            transform: translateY(-2px);
        }
        .pair-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .pair-body {
            padding: 20px;
        }
        .signal-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        .signal-strong-buy { background-color: #28a745; color: white; }
        .signal-buy { background-color: #5cb85c; color: white; }
        .signal-neutral { background-color: #ffc107; color: #212529; }
        .signal-sell { background-color: #fd7e14; color: white; }
        .signal-strong-sell { background-color: #dc3545; color: white; }

        .metric-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .metric-label {
            font-weight: 600;
            color: #6c757d;
        }
        .metric-value {
            font-weight: bold;
        }

        .indicators-table {
            width: 100%;
            margin-top: 15px;
        }
        .indicators-table th {
            background-color: #f8f9fa;
            padding: 8px;
            border-bottom: 2px solid #dee2e6;
            font-size: 0.9rem;
        }
        .indicators-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.85rem;
        }

        .impact-bar {
            height: 15px;
            border-radius: 7px;
            position: relative;
            background-color: #e9ecef;
            overflow: hidden;
        }
        .impact-fill {
            height: 100%;
            border-radius: 7px;
            transition: width 0.3s ease;
        }
        .impact-positive { background-color: #28a745; }
        .impact-negative { background-color: #dc3545; }

        .control-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .btn-collect {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            font-weight: bold;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s;
        }
        .btn-collect:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        .btn-collect:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .loading-spinner {
            display: none;
            margin-left: 10px;
        }

        .no-data-message {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .footer {
            margin-top: 50px;
            padding: 20px 0;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                Forex Analysis Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/logs">
                    <i class="fas fa-file-alt me-1"></i>
                    Logs
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Status Card -->
        <div class="status-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="mb-2">
                        <span id="status-indicator" class="status-indicator status-ready"></span>
                        System Status
                    </h5>
                    <p id="status-message" class="mb-0">{{ status.message }}</p>
                    {% if status.last_success %}
                    <small class="text-muted">Last successful collection: {{ status.last_success }}</small>
                    {% endif %}
                </div>
                <div class="col-md-4 text-end">
                    <button id="collect-btn" class="btn btn-collect" onclick="collectData()">
                        <i class="fas fa-download me-2"></i>
                        Collect Data
                        <div class="loading-spinner spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="row">
                <div class="col-md-6">
                    <h6>Currency Pair Filter</h6>
                    <select id="pair-filter" class="form-select" onchange="filterPairs()">
                        <option value="">All Pairs</option>
                        <option value="EUR/USD">EUR/USD</option>
                        <option value="USD/JPY">USD/JPY</option>
                        <option value="GBP/USD">GBP/USD</option>
                        <option value="USD/CHF">USD/CHF</option>
                        <option value="USD/CAD">USD/CAD</option>
                        <option value="AUD/USD">AUD/USD</option>
                        <option value="NZD/USD">NZD/USD</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <h6>Quick Actions</h6>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshPage()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Refresh Page
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="checkData()">
                            <i class="fas fa-search me-1"></i>
                            Check Data
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportAllData()">
                            <i class="fas fa-file-export me-1"></i>
                            Export All
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Currency Pairs -->
        <div id="pairs-container">
            {% if dashboard_data and dashboard_data.currency_pairs %}
                {% for pair, data in dashboard_data.currency_pairs.items() %}
                <div class="pair-card" data-pair="{{ pair }}">
                    <div class="pair-header">
                        <h4 class="mb-0">{{ pair }}</h4>
                        <span class="signal-badge signal-{{ data.signal.lower().replace(' ', '-') }}">
                            {{ data.signal }}
                        </span>
                    </div>
                    <div class="pair-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="metric-row">
                                    <span class="metric-label">Score:</span>
                                    <span class="metric-value">{{ "%.2f"|format(data.score) }}</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Confidence:</span>
                                    <span class="metric-value">{{ data.confidence }}%</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Optimal Horizon:</span>
                                    <span class="metric-value">{{ data.optimal_horizon }} days</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-outline-primary btn-sm" onclick="exportPairData('{{ pair }}')">
                                    <i class="fas fa-download me-1"></i>
                                    Export Data
                                </button>
                            </div>
                        </div>

                        {% if data.top_indicators %}
                        <h6 class="mt-3 mb-2">Top Indicators</h6>
                        <table class="indicators-table table table-sm">
                            <thead>
                                <tr>
                                    <th>Indicator</th>
                                    <th>Impact</th>
                                    <th>Visual</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for indicator in data.top_indicators[:10] %}
                                <tr>
                                    <td>{{ indicator.name }}</td>
                                    <td>{{ "%.2f"|format(indicator.impact) }}</td>
                                    <td>
                                        <div class="impact-bar">
                                            <div class="impact-fill impact-{{ 'positive' if indicator.impact > 0 else 'negative' }}"
                                                 style="width: {{ [100, (indicator.impact|abs * 50)]|min }}%"></div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-data-message">
                    <i class="fas fa-chart-line fa-3x mb-3 text-muted"></i>
                    <h4>No Data Available</h4>
                    <p>Click "Collect Data" to fetch the latest economic indicators and analysis.</p>
                </div>
            {% endif %}
        </div>

        {% if dashboard_data %}
        <div class="text-center text-muted">
            <small>Last updated: {{ dashboard_data.timestamp }}</small>
        </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="container">
            <p>&copy; 2025 Forex Price Action Analysis System - Standalone Web Application</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let statusCheckInterval;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            startStatusPolling();
        });

        // Start polling for status updates
        function startStatusPolling() {
            statusCheckInterval = setInterval(updateStatus, 5000); // Check every 5 seconds
        }

        // Update system status
        function updateStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.getElementById('status-indicator');
                    const message = document.getElementById('status-message');
                    const collectBtn = document.getElementById('collect-btn');
                    const spinner = collectBtn.querySelector('.loading-spinner');

                    message.textContent = data.message;

                    if (data.running) {
                        indicator.className = 'status-indicator status-running';
                        collectBtn.disabled = true;
                        spinner.style.display = 'inline-block';
                    } else {
                        indicator.className = data.last_success ? 'status-indicator status-success' : 'status-indicator status-ready';
                        collectBtn.disabled = false;
                        spinner.style.display = 'none';

                        // If data collection just completed, refresh the page to show new data
                        if (data.message.includes('completed successfully') && !window.dataCollectionCompleted) {
                            window.dataCollectionCompleted = true;
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000); // Wait 2 seconds then refresh
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating status:', error);
                });
        }

        // Collect data
        function collectData() {
            const collectBtn = document.getElementById('collect-btn');
            const spinner = collectBtn.querySelector('.loading-spinner');

            collectBtn.disabled = true;
            spinner.style.display = 'inline-block';

            fetch('/collect-data', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Data collection started, status polling will handle updates
                        console.log('Data collection started');
                    } else {
                        alert('Failed to start data collection: ' + data.message);
                        collectBtn.disabled = false;
                        spinner.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error starting data collection:', error);
                    alert('Error starting data collection. Please try again.');
                    collectBtn.disabled = false;
                    spinner.style.display = 'none';
                });
        }

        // Filter currency pairs
        function filterPairs() {
            const filter = document.getElementById('pair-filter').value;
            const pairs = document.querySelectorAll('.pair-card');

            console.log(`Filtering for: "${filter}"`);
            console.log(`Found ${pairs.length} pair cards`);

            pairs.forEach(pair => {
                const pairName = pair.getAttribute('data-pair');
                console.log(`Checking pair: ${pairName}`);

                if (filter === '' || pairName === filter) {
                    pair.style.display = 'block';
                    console.log(`Showing: ${pairName}`);
                } else {
                    pair.style.display = 'none';
                    console.log(`Hiding: ${pairName}`);
                }
            });
        }

        // Refresh page
        function refreshPage() {
            window.location.reload();
        }

        // Check data availability
        function checkData() {
            fetch('/dashboard-data')
                .then(response => response.json())
                .then(data => {
                    if (data.currency_pairs && Object.keys(data.currency_pairs).length > 0) {
                        alert(`Data found: ${Object.keys(data.currency_pairs).length} currency pairs available.\nLast updated: ${data.timestamp || 'Unknown'}\n\nRefreshing page to display data...`);
                        window.location.reload();
                    } else {
                        alert('No currency pair data found.\nPlease click "Collect Data" first.');
                    }
                })
                .catch(error => {
                    console.error('Error checking data:', error);
                    alert('Error checking data. Please try collecting data first.');
                });
        }

        // Export pair data
        function exportPairData(pair) {
            window.open(`/export/${pair}`, '_blank');
        }

        // Export all data
        function exportAllData() {
            window.open('/api/export-all-csv', '_blank');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
        });
    </script>
</body>
</html>
