#!/usr/bin/env python3
"""
Test data collection without dashboard update errors
"""

import os
import sys
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_data_collection():
    """Test the data collection process"""
    
    print("🧪 TESTING DATA COLLECTION")
    print("=" * 40)
    
    try:
        from src.main import run_scraper, update_dashboard
        
        print("📊 Testing scraper...")
        scraper_success = run_scraper()
        
        if scraper_success:
            print("✅ Scraper completed successfully")
        else:
            print("❌ Scraper failed")
            return False
        
        print("\n🎨 Testing dashboard update...")
        dashboard_success = update_dashboard()
        
        if dashboard_success:
            print("✅ Dashboard update completed successfully")
        else:
            print("❌ Dashboard update failed")
            return False
        
        # Check if data file exists
        data_file = 'data/dashboard_data.json'
        if os.path.exists(data_file):
            print(f"✅ Data file exists: {data_file}")
            
            with open(data_file, 'r') as f:
                data = json.load(f)
            
            pairs = data.get('currency_pairs', {})
            print(f"✅ Found {len(pairs)} currency pairs")
            
            for pair, pair_data in pairs.items():
                signal = pair_data.get('signal', 'Unknown')
                score = pair_data.get('score', 0)
                print(f"   - {pair}: {signal} (Score: {score})")
            
        else:
            print(f"❌ Data file not found: {data_file}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def main():
    """Main test function"""
    
    success = test_data_collection()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Data collection is working correctly")
        print("✅ No more dashboard update errors")
        print("\n💡 Next steps:")
        print("   1. Restart your Flask app: python start_system.py")
        print("   2. Click 'Collect Data' in the web interface")
        print("   3. Click 'Refresh Page' to see the results")
    else:
        print("\n❌ TESTS FAILED")
        print("🔧 Check the error messages above")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
