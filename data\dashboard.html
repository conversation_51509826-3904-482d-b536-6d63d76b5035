<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forex Analysis Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #212529;
            padding-bottom: 50px;
        }
        .header {
            background-color: #343a40;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        .header p {
            margin: 10px 0 0;
            font-size: 1.2rem;
            opacity: 0.8;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 8px;
            overflow: hidden;
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            padding: 12px 20px;
        }
        .card-body {
            padding: 20px;
        }
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
            font-weight: bold;
            padding: 10px 20px;
            font-size: 1.1rem;
            margin: 20px 0;
        }
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        .pair-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            padding: 20px;
        }
        .pair-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 15px;
        }
        .pair-header h2 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: bold;
        }
        .signal-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
        }
        .strong-buy {
            background-color: #28a745;
        }
        .buy {
            background-color: #5cb85c;
        }
        .neutral {
            background-color: #ffc107;
            color: #212529;
        }
        .sell {
            background-color: #dc3545;
        }
        .strong-sell {
            background-color: #c82333;
        }
        .pair-details {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .detail-item {
            flex: 1;
            min-width: 150px;
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
            color: #6c757d;
        }
        .detail-value {
            font-size: 1.2rem;
        }
        .section {
            margin-top: 25px;
        }
        .section h3 {
            font-size: 1.4rem;
            margin-bottom: 15px;
            color: #343a40;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        .indicators-table, .history-table {
            width: 100%;
            border-collapse: collapse;
        }
        .indicators-table th, .history-table th {
            background-color: #f8f9fa;
            padding: 10px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
        }
        .indicators-table td, .history-table td {
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .impact-bar-container {
            width: 100px;
            height: 20px;
            background-color: #f8f9fa;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
        }
        .impact-bar {
            height: 100%;
            position: absolute;
            top: 0;
        }
        .impact-positive {
            background-color: #28a745;
            left: 50%;
        }
        .impact-negative {
            background-color: #dc3545;
            right: 50%;
        }
        .currency-selector {
            margin: 20px 0;
        }
        .currency-selector select {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ced4da;
            font-size: 1rem;
        }
        .data-export {
            margin-top: 30px;
            text-align: center;
        }
        .data-export h3 {
            margin-bottom: 15px;
        }
        .data-export .btn {
            margin: 0 10px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .last-updated {
            text-align: right;
            font-style: italic;
            color: #6c757d;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Forex Analysis Dashboard</h1>
        <p>Economic Indicators Impact on Major Currency Pairs</p>
    </div>

    <div class="container">
        <div class="text-center">
            <button id="collect-data" class="btn btn-primary">Collect Latest Economic Data</button>
        </div>

        <div class="currency-selector">
            <select id="pair-selector" class="form-select">
                <option value="EUR/USD">EUR/USD</option>
                <option value="USD/JPY">USD/JPY</option>
                <option value="GBP/USD">GBP/USD</option>
                <option value="USD/CHF">USD/CHF</option>
                <option value="USD/CAD">USD/CAD</option>
                <option value="AUD/USD">AUD/USD</option>
                <option value="NZD/USD">NZD/USD</option>
            </select>
        </div>

        <div id="pair-container">
            
    <div class="pair-card">
        <div class="pair-header">
            <h2>EUR/USD</h2>
            <div class="signal-badge sell">Sell</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">-0.89</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">80%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">7 days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            <table class='indicators-table'><tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr><tr><td>Interest Rate</td><td>-0.9</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 45.0px;'></div></div></td></tr><tr><td>Unemployment Rate</td><td>0.7</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 35.0px;'></div></div></td></tr><tr><td>GDP Growth Rate</td><td>0.21</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 10.5px;'></div></div></td></tr><tr><td>Consumer Confidence</td><td>-0.08</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 4.0px;'></div></div></td></tr><tr><td>Services PMI</td><td>-0.07</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 3.5000000000000004px;'></div></div></td></tr><tr><td>Inflation Rate</td><td>-0.04</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 2.0px;'></div></div></td></tr><tr><td>Manufacturing PMI</td><td>-0.04</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 2.0px;'></div></div></td></tr><tr><td>Retail Sales MoM</td><td>-0.01</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 0.5px;'></div></div></td></tr><tr><td>Business Confidence</td><td>-0.0</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 0.0px;'></div></div></td></tr></table>
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            <table class='history-table'><tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr><tr><td>2025-05-27 18:28</td><td>-0.89</td><td>Sell</td><td>80%</td></tr><tr><td>2025-05-27 18:15</td><td>-0.89</td><td>Sell</td><td>80%</td></tr><tr><td>2025-05-26 20:33</td><td>-0.89</td><td>Sell</td><td>80%</td></tr></table>
        </div>
    </div>
    
    <div class="pair-card">
        <div class="pair-header">
            <h2>USD/JPY</h2>
            <div class="signal-badge strong-buy">Strong Buy</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">1.8</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">80%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">3 days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            <table class='indicators-table'><tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr><tr><td>Interest Rate</td><td>0.9</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 45.0px;'></div></div></td></tr><tr><td>Unemployment Rate</td><td>0.59</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 29.5px;'></div></div></td></tr><tr><td>Inflation Rate</td><td>-0.52</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 26.0px;'></div></div></td></tr><tr><td>Consumer Confidence</td><td>0.25</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 12.5px;'></div></div></td></tr><tr><td>Business Confidence</td><td>0.19</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 9.5px;'></div></div></td></tr><tr><td>Manufacturing PMI</td><td>0.14</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 7.000000000000001px;'></div></div></td></tr><tr><td>Services PMI</td><td>0.14</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 7.000000000000001px;'></div></div></td></tr><tr><td>GDP Growth Rate</td><td>-0.03</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 1.5px;'></div></div></td></tr><tr><td>Retail Sales MoM</td><td>0.01</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 0.5px;'></div></div></td></tr></table>
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            <table class='history-table'><tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr><tr><td>2025-05-27 18:28</td><td>1.8</td><td>Strong Buy</td><td>80%</td></tr><tr><td>2025-05-27 18:15</td><td>1.8</td><td>Strong Buy</td><td>80%</td></tr><tr><td>2025-05-26 20:33</td><td>1.8</td><td>Strong Buy</td><td>80%</td></tr></table>
        </div>
    </div>
    
    <div class="pair-card">
        <div class="pair-header">
            <h2>GBP/USD</h2>
            <div class="signal-badge neutral">Neutral</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">-0.29</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">80%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">15 days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            <table class='indicators-table'><tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr><tr><td>Inflation Rate</td><td>0.48</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 24.0px;'></div></div></td></tr><tr><td>GDP Growth Rate</td><td>0.35</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 17.5px;'></div></div></td></tr><tr><td>Manufacturing PMI</td><td>-0.29</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 14.499999999999998px;'></div></div></td></tr><tr><td>Business Confidence</td><td>-0.13</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 6.5px;'></div></div></td></tr><tr><td>Retail Sales MoM</td><td>0.12</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 6.0px;'></div></div></td></tr><tr><td>Interest Rate</td><td>-0.11</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 5.5px;'></div></div></td></tr><tr><td>Unemployment Rate</td><td>0.1</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 5.0px;'></div></div></td></tr><tr><td>Consumer Confidence</td><td>-0.1</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 5.0px;'></div></div></td></tr><tr><td>Services PMI</td><td>0.01</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 0.5px;'></div></div></td></tr></table>
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            <table class='history-table'><tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr><tr><td>2025-05-27 18:28</td><td>-0.29</td><td>Neutral</td><td>80%</td></tr><tr><td>2025-05-27 18:15</td><td>-0.29</td><td>Neutral</td><td>80%</td></tr><tr><td>2025-05-26 20:33</td><td>-0.29</td><td>Neutral</td><td>80%</td></tr></table>
        </div>
    </div>
    
    <div class="pair-card">
        <div class="pair-header">
            <h2>USD/CHF</h2>
            <div class="signal-badge buy">Buy</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">0.67</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">80%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">15 days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            <table class='indicators-table'><tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr><tr><td>Interest Rate</td><td>0.9</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 45.0px;'></div></div></td></tr><tr><td>Inflation Rate</td><td>0.8</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 40.0px;'></div></div></td></tr><tr><td>Unemployment Rate</td><td>0.49</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 24.5px;'></div></div></td></tr><tr><td>GDP Growth Rate</td><td>-0.35</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 17.5px;'></div></div></td></tr><tr><td>Consumer Confidence</td><td>0.25</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 12.5px;'></div></div></td></tr><tr><td>Business Confidence</td><td>0.19</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 9.5px;'></div></div></td></tr><tr><td>Manufacturing PMI</td><td>0.14</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 7.000000000000001px;'></div></div></td></tr><tr><td>Services PMI</td><td>0.14</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 7.000000000000001px;'></div></div></td></tr><tr><td>Retail Sales MoM</td><td>0.01</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 0.5px;'></div></div></td></tr></table>
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            <table class='history-table'><tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr><tr><td>2025-05-27 18:28</td><td>0.67</td><td>Buy</td><td>80%</td></tr><tr><td>2025-05-27 18:15</td><td>0.67</td><td>Buy</td><td>80%</td></tr><tr><td>2025-05-26 20:33</td><td>0.67</td><td>Buy</td><td>80%</td></tr></table>
        </div>
    </div>
    
    <div class="pair-card">
        <div class="pair-header">
            <h2>USD/CAD</h2>
            <div class="signal-badge buy">Buy</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">0.61</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">80%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">15 days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            <table class='indicators-table'><tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr><tr><td>Interest Rate</td><td>0.79</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 39.5px;'></div></div></td></tr><tr><td>Unemployment Rate</td><td>-0.7</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 35.0px;'></div></div></td></tr><tr><td>GDP Growth Rate</td><td>-0.31</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 15.5px;'></div></div></td></tr><tr><td>Consumer Confidence</td><td>0.25</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 12.5px;'></div></div></td></tr><tr><td>Inflation Rate</td><td>0.24</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 12.0px;'></div></div></td></tr><tr><td>Business Confidence</td><td>0.19</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 9.5px;'></div></div></td></tr><tr><td>Manufacturing PMI</td><td>0.14</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 7.000000000000001px;'></div></div></td></tr><tr><td>Services PMI</td><td>0.14</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 7.000000000000001px;'></div></div></td></tr><tr><td>Retail Sales MoM</td><td>0.01</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 0.5px;'></div></div></td></tr></table>
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            <table class='history-table'><tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr><tr><td>2025-05-27 18:28</td><td>0.61</td><td>Buy</td><td>80%</td></tr><tr><td>2025-05-27 18:15</td><td>0.61</td><td>Buy</td><td>80%</td></tr><tr><td>2025-05-26 20:33</td><td>0.61</td><td>Buy</td><td>80%</td></tr></table>
        </div>
    </div>
    
    <div class="pair-card">
        <div class="pair-header">
            <h2>AUD/USD</h2>
            <div class="signal-badge neutral">Neutral</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">-0.1</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">80%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">15 days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            <table class='indicators-table'><tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr><tr><td>Consumer Confidence</td><td>0.46</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 23.0px;'></div></div></td></tr><tr><td>GDP Growth Rate</td><td>0.31</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 15.5px;'></div></div></td></tr><tr><td>Interest Rate</td><td>-0.29</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 14.499999999999998px;'></div></div></td></tr><tr><td>Manufacturing PMI</td><td>0.1</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 5.0px;'></div></div></td></tr><tr><td>Inflation Rate</td><td>0.04</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 2.0px;'></div></div></td></tr><tr><td>Unemployment Rate</td><td>-0.04</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 2.0px;'></div></div></td></tr><tr><td>Services PMI</td><td>0.03</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 1.5px;'></div></div></td></tr><tr><td>Retail Sales MoM</td><td>0.03</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 1.5px;'></div></div></td></tr><tr><td>Business Confidence</td><td>-0.0</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 0.0px;'></div></div></td></tr></table>
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            <table class='history-table'><tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr><tr><td>2025-05-27 18:28</td><td>-0.1</td><td>Neutral</td><td>80%</td></tr><tr><td>2025-05-27 18:15</td><td>-0.1</td><td>Neutral</td><td>80%</td></tr><tr><td>2025-05-26 20:33</td><td>-0.1</td><td>Neutral</td><td>80%</td></tr></table>
        </div>
    </div>
    
    <div class="pair-card">
        <div class="pair-header">
            <h2>NZD/USD</h2>
            <div class="signal-badge sell">Sell</div>
        </div>
        <div class="pair-details">
            <div class="detail-item">
                <span class="detail-label">Score:</span>
                <span class="detail-value">-0.35</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Confidence:</span>
                <span class="detail-value">80%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Optimal Horizon:</span>
                <span class="detail-value">15 days</span>
            </div>
        </div>

        <div class="section">
            <h3>Top Indicators</h3>
            <table class='indicators-table'><tr><th>Indicator</th><th>Impact</th><th>Visualization</th></tr><tr><td>Interest Rate</td><td>-0.45</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 22.5px;'></div></div></td></tr><tr><td>Consumer Confidence</td><td>0.45</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 22.5px;'></div></div></td></tr><tr><td>Services PMI</td><td>-0.41</td><td><div class='impact-bar-container'><div class='impact-bar impact-negative' style='width: 20.5px;'></div></div></td></tr><tr><td>GDP Growth Rate</td><td>0.35</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 17.5px;'></div></div></td></tr><tr><td>Unemployment Rate</td><td>0.31</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 15.5px;'></div></div></td></tr><tr><td>Manufacturing PMI</td><td>0.23</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 11.5px;'></div></div></td></tr><tr><td>Business Confidence</td><td>0.2</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 10.0px;'></div></div></td></tr><tr><td>Inflation Rate</td><td>0.08</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 4.0px;'></div></div></td></tr><tr><td>Retail Sales MoM</td><td>0.08</td><td><div class='impact-bar-container'><div class='impact-bar impact-positive' style='width: 4.0px;'></div></div></td></tr></table>
        </div>

        <div class="section">
            <h3>Historical Data (Last 3 Readings)</h3>
            <table class='history-table'><tr><th>Date</th><th>Score</th><th>Signal</th><th>Confidence</th></tr><tr><td>2025-05-27 18:28</td><td>-0.35</td><td>Sell</td><td>80%</td></tr><tr><td>2025-05-27 18:15</td><td>-0.35</td><td>Sell</td><td>80%</td></tr><tr><td>2025-05-26 20:33</td><td>-0.35</td><td>Sell</td><td>80%</td></tr></table>
        </div>
    </div>
    
        </div>

        <div class="data-export">
            <h3>Export Data</h3>
            <p>Export economic indicators data for further analysis:</p>
            <button id="export-csv" class="btn btn-outline-primary">Export to CSV</button>
            <button id="export-json" class="btn btn-outline-secondary">Export to JSON</button>
        </div>

        <div class="last-updated">
            Last Updated: 2025-05-27 18:28:36
        </div>

        <div class="footer">
            <p>Forex Price Action Analysis System &copy; 2025</p>
        </div>
    </div>

    <script>
        // Load dashboard data
        const dashboardData = {
            timestamp: "2025-05-27 18:28:36",
            currency_pairs: {}
        };

        // Function to initialize the dashboard
        function initDashboard() {
            // Fetch the dashboard data
            fetch('/data/dashboard_data.json')
                .then(response => response.json())
                .then(data => {
                    // Store the data
                    Object.assign(dashboardData, data);
                    
                    // Update the UI
                    updateUI();
                })
                .catch(error => {
                    console.error('Error loading dashboard data:', error);
                });
        }

        // Function to update the UI based on selected pair
        function updateUI() {
            const pairSelector = document.getElementById('pair-selector');
            const selectedPair = pairSelector.value;
            
            // Show only the selected pair
            const pairCards = document.querySelectorAll('.pair-card');
            pairCards.forEach(card => {
                const pairHeader = card.querySelector('.pair-header h2');
                if (pairHeader && pairHeader.textContent === selectedPair) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Event listener for pair selector
        document.getElementById('pair-selector').addEventListener('change', updateUI);

        // Event listener for collect data button
        document.getElementById('collect-data').addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'Collecting Data...';
            
            // Make a request to the server to collect data
            fetch('/api/collect-data', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to show new data
                        window.location.reload();
                    } else {
                        alert('Failed to collect data: ' + data.error);
                        this.disabled = false;
                        this.textContent = 'Collect Latest Economic Data';
                    }
                })
                .catch(error => {
                    console.error('Error collecting data:', error);
                    alert('Error collecting data. Please try again.');
                    this.disabled = false;
                    this.textContent = 'Collect Latest Economic Data';
                });
        });

        // Event listener for export buttons
        document.getElementById('export-csv').addEventListener('click', function() {
            // Get the selected pair
            const selectedPair = document.getElementById('pair-selector').value;
            
            // Create a CSV string
            let csv = 'Indicator,Impact\n';
            const pairData = dashboardData.currency_pairs[selectedPair];
            if (pairData && pairData.top_indicators) {
                pairData.top_indicators.forEach(indicator => {
                    csv += `${indicator.name},${indicator.impact}\n`;
                });
            }
            
            // Create a download link
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${selectedPair.replace('/', '')}_indicators.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        });

        document.getElementById('export-json').addEventListener('click', function() {
            // Get the selected pair
            const selectedPair = document.getElementById('pair-selector').value;
            
            // Get the pair data
            const pairData = dashboardData.currency_pairs[selectedPair];
            
            // Create a download link
            const blob = new Blob([JSON.stringify(pairData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${selectedPair.replace('/', '')}_data.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        });

        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', initDashboard);
    </script>
</body>
</html>
