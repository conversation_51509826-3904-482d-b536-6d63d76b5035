#!/usr/bin/env python3
"""
Test template rendering with actual data
"""

import os
import json
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_template_rendering():
    """Test if the template can render with actual data"""
    
    print("🎨 TESTING TEMPLATE RENDERING")
    print("=" * 40)
    
    # Load actual dashboard data
    data_file = 'data/dashboard_data.json'
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return False
    
    try:
        with open(data_file, 'r') as f:
            dashboard_data = json.load(f)
        
        print(f"✅ Loaded data with {len(dashboard_data.get('currency_pairs', {}))} pairs")
        
        # Test Flask template rendering
        from flask import Flask, render_template
        
        app = Flask(__name__, template_folder='src/templates')
        
        with app.app_context():
            try:
                html = render_template('dashboard.html', 
                                     dashboard_data=dashboard_data,
                                     status={'message': 'Test status'})
                
                print("✅ Template rendered successfully")
                print(f"   HTML length: {len(html)} characters")
                
                # Check if currency pairs are in the HTML
                pairs_found = 0
                for pair in dashboard_data.get('currency_pairs', {}):
                    if pair in html:
                        pairs_found += 1
                
                print(f"   Currency pairs found in HTML: {pairs_found}")
                
                if pairs_found > 0:
                    print("✅ Currency pairs are being rendered in template")
                    return True
                else:
                    print("❌ Currency pairs not found in rendered HTML")
                    return False
                
            except Exception as e:
                print(f"❌ Template rendering failed: {e}")
                return False
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False

def test_data_structure():
    """Test the data structure"""
    
    print("\n📊 TESTING DATA STRUCTURE")
    print("=" * 40)
    
    data_file = 'data/dashboard_data.json'
    
    try:
        with open(data_file, 'r') as f:
            data = json.load(f)
        
        print("✅ JSON is valid")
        
        # Check required fields
        required_fields = ['timestamp', 'currency_pairs']
        for field in required_fields:
            if field in data:
                print(f"✅ Has {field}")
            else:
                print(f"❌ Missing {field}")
                return False
        
        # Check currency pairs structure
        pairs = data.get('currency_pairs', {})
        if len(pairs) == 0:
            print("❌ No currency pairs found")
            return False
        
        print(f"✅ Found {len(pairs)} currency pairs")
        
        # Check first pair structure
        first_pair = list(pairs.keys())[0]
        first_data = pairs[first_pair]
        
        required_pair_fields = ['score', 'signal', 'confidence', 'optimal_horizon']
        for field in required_pair_fields:
            if field in first_data:
                print(f"✅ Pair has {field}: {first_data[field]}")
            else:
                print(f"❌ Pair missing {field}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing data structure: {e}")
        return False

def main():
    """Main test function"""
    
    data_ok = test_data_structure()
    template_ok = test_template_rendering()
    
    if data_ok and template_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Data structure is correct")
        print("✅ Template rendering works")
        print("\n💡 The issue might be in the Flask app routing")
        print("   Try restarting the Flask app and check browser console")
    else:
        print("\n❌ TESTS FAILED")
        if not data_ok:
            print("🔧 Fix the data structure first")
        if not template_ok:
            print("🔧 Fix the template rendering")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
