<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Logs - Forex Analysis Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .log-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .log-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        .log-content {
            max-height: 400px;
            overflow-y: auto;
            padding: 0;
        }
        .log-line {
            padding: 8px 20px;
            border-bottom: 1px solid #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
        }
        .log-line:hover {
            background-color: #f8f9fa;
        }
        .log-level-INFO { color: #28a745; }
        .log-level-WARNING { color: #ffc107; }
        .log-level-ERROR { color: #dc3545; }
        .log-level-DEBUG { color: #6c757d; }
        .log-timestamp {
            color: #6c757d;
            font-weight: bold;
        }
        .controls {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                Forex Analysis Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    Dashboard
                </a>
                <a class="nav-link active" href="/logs">
                    <i class="fas fa-file-alt me-1"></i>
                    Logs
                </a>
                <a class="nav-link" href="/health">
                    <i class="fas fa-heartbeat me-1"></i>
                    Health
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        System Logs
                    </h4>
                    <small class="text-muted">Last 50 lines from each log file</small>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-primary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh Logs
                    </button>
                </div>
            </div>
        </div>

        {% if logs %}
            {% for log in logs %}
            <div class="log-container">
                <div class="log-header">
                    <i class="fas fa-file me-2"></i>
                    {{ log.file }}
                </div>
                <div class="log-content">
                    {% if log.lines %}
                        {% for line in log.lines %}
                        <div class="log-line">{{ line|safe }}</div>
                        {% endfor %}
                    {% else %}
                        <div class="log-line text-muted">No log entries found</div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="log-container">
                <div class="log-content">
                    <div class="log-line text-muted text-center py-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No log files found
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshLogs() {
            window.location.reload();
        }
        
        // Auto-refresh logs every 30 seconds
        setInterval(refreshLogs, 30000);
        
        // Highlight log levels
        document.addEventListener('DOMContentLoaded', function() {
            const logLines = document.querySelectorAll('.log-line');
            logLines.forEach(line => {
                const text = line.textContent;
                if (text.includes(' - INFO - ')) {
                    line.classList.add('log-level-INFO');
                } else if (text.includes(' - WARNING - ')) {
                    line.classList.add('log-level-WARNING');
                } else if (text.includes(' - ERROR - ')) {
                    line.classList.add('log-level-ERROR');
                } else if (text.includes(' - DEBUG - ')) {
                    line.classList.add('log-level-DEBUG');
                }
                
                // Highlight timestamps
                const timestampMatch = text.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}/);
                if (timestampMatch) {
                    const timestamp = timestampMatch[0];
                    line.innerHTML = line.innerHTML.replace(timestamp, `<span class="log-timestamp">${timestamp}</span>`);
                }
            });
        });
    </script>
</body>
</html>
