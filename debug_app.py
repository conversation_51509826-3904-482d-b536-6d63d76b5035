#!/usr/bin/env python3
"""
Debug version of the Flask app to test data display
"""

import os
import json
import sys
from flask import Flask, render_template, jsonify

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

app = Flask(__name__, template_folder='src/templates')

@app.route('/')
def debug_index():
    """Debug version of the main page"""
    
    print("🔍 DEBUG: Loading dashboard page")
    
    # Check if data file exists
    data_file = 'data/dashboard_data.json'
    print(f"🔍 DEBUG: Checking for data file: {data_file}")
    print(f"🔍 DEBUG: File exists: {os.path.exists(data_file)}")
    
    if os.path.exists(data_file):
        try:
            with open(data_file, 'r') as f:
                dashboard_data = json.load(f)
            
            pairs = dashboard_data.get('currency_pairs', {})
            print(f"🔍 DEBUG: Loaded {len(pairs)} currency pairs")
            
            for pair, data in pairs.items():
                signal = data.get('signal', 'No signal')
                score = data.get('score', 'No score')
                print(f"🔍 DEBUG: {pair} -> {signal} (Score: {score})")
            
            # Create a simple status
            status = {'message': 'Debug mode - data loaded'}
            
            print("🔍 DEBUG: Rendering template with data")
            return render_template('dashboard.html', 
                                 dashboard_data=dashboard_data,
                                 status=status)
            
        except Exception as e:
            print(f"❌ DEBUG: Error loading data: {e}")
            return f"<h1>Error loading data: {e}</h1>"
    
    else:
        print("❌ DEBUG: No data file found")
        return render_template('dashboard.html', 
                             dashboard_data=None,
                             status={'message': 'No data file found'})

@app.route('/debug-data')
def debug_data():
    """Show raw data for debugging"""
    
    data_file = 'data/dashboard_data.json'
    
    if os.path.exists(data_file):
        with open(data_file, 'r') as f:
            data = json.load(f)
        return jsonify(data)
    else:
        return jsonify({'error': 'No data file found'})

@app.route('/debug-template')
def debug_template():
    """Test template with minimal data"""
    
    test_data = {
        'timestamp': '2025-01-11 12:00:00',
        'currency_pairs': {
            'EUR/USD': {
                'score': -0.89,
                'signal': 'Sell',
                'confidence': 80,
                'optimal_horizon': 7,
                'top_indicators': [
                    {'name': 'Interest Rate', 'impact': -0.9},
                    {'name': 'Unemployment Rate', 'impact': 0.7}
                ]
            },
            'USD/JPY': {
                'score': 1.8,
                'signal': 'Strong Buy',
                'confidence': 80,
                'optimal_horizon': 3,
                'top_indicators': [
                    {'name': 'GDP Growth', 'impact': 1.2},
                    {'name': 'Inflation Rate', 'impact': 0.8}
                ]
            }
        }
    }
    
    status = {'message': 'Test data loaded'}
    
    return render_template('dashboard.html', 
                         dashboard_data=test_data,
                         status=status)

if __name__ == '__main__':
    print("🚀 Starting DEBUG Flask App")
    print("=" * 40)
    print("📊 Routes available:")
    print("   http://localhost:5001/           - Main debug page")
    print("   http://localhost:5001/debug-data - Raw JSON data")
    print("   http://localhost:5001/debug-template - Test template")
    print("=" * 40)
    
    app.run(host='127.0.0.1', port=5001, debug=True)
