#!/usr/bin/env python3
"""
Forex Analysis System - Easy Startup Script

This script provides an easy way to start the Forex Analysis System.
Simply double-click this file or run it from command line.
"""

import os
import sys
import webbrowser
import time
import threading
from datetime import datetime

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'flask',
        'requests', 
        'beautifulsoup4',
        'pandas',
        'sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Please install missing packages:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed")
    return True

def setup_directories():
    """Create necessary directories"""
    directories = [
        'data',
        'data/cache',
        'data/exports', 
        'data/economic_indicators',
        'instance'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Directory structure created")

def open_browser_delayed():
    """Open browser after a short delay"""
    time.sleep(3)  # Wait for server to start
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Opening browser at http://localhost:5000")
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print("   Please manually open: http://localhost:5000")

def main():
    """Main startup function"""
    print("🚀 Starting Forex Analysis System")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        input("\nPress Enter to exit...")
        return
    
    # Setup directories
    setup_directories()
    
    # Start browser in background
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Import and start the Flask app
    try:
        from app import app
        print("✅ Flask application loaded")
        print("🌐 Starting web server...")
        print("📊 Dashboard will be available at: http://localhost:5000")
        print("🔄 The system will automatically open in your browser")
        print("\n" + "=" * 50)
        print("📝 INSTRUCTIONS:")
        print("   1. Wait for the browser to open")
        print("   2. Click 'Collect Data' to fetch economic indicators")
        print("   3. View analysis results on the dashboard")
        print("   4. Use the export features to save data")
        print("   5. Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start the Flask development server
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,  # Disable debug mode for cleaner output
            use_reloader=False  # Disable reloader to prevent double startup
        )
        
    except ImportError as e:
        print(f"❌ Error importing Flask application: {e}")
        print("   Please check that all files are in the correct location")
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
