#!/usr/bin/env python3
"""
Quick test to verify the system works without pandas
"""

def test_basic_imports():
    """Test basic imports"""
    print("Testing basic imports...")
    
    try:
        import flask
        print("✅ Flask imported")
    except ImportError as e:
        print(f"❌ Flask failed: {e}")
        return False
    
    try:
        import requests
        print("✅ Requests imported")
    except ImportError as e:
        print(f"❌ Requests failed: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup imported")
    except ImportError as e:
        print(f"❌ BeautifulSoup failed: {e}")
        return False
    
    try:
        import csv
        print("✅ CSV module available")
    except ImportError as e:
        print(f"❌ CSV failed: {e}")
        return False
    
    return True

def test_app_creation():
    """Test Flask app creation"""
    print("\nTesting Flask app creation...")
    
    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from app import create_app
        app = create_app()
        print("✅ Flask app created successfully")
        return True
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        return False

def main():
    print("🧪 QUICK SYSTEM TEST")
    print("=" * 30)
    
    if test_basic_imports() and test_app_creation():
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ System should work now")
        print("\n🚀 Try running:")
        print("   python run.py")
        return True
    else:
        print("\n❌ TESTS FAILED")
        print("🔧 Please install missing dependencies:")
        print("   pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
