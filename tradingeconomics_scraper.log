2025-05-26 19:43:14,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 19:43:14,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 19:43:14,231 - werkzeug - INFO -  * Restarting with stat
2025-05-26 19:43:15,241 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 19:43:15,242 - werkzeug - INFO -  * Debugger PIN: 192-990-882
2025-05-26 19:43:49,229 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:43:49] "GET / HTTP/1.1" 200 -
2025-05-26 19:43:49,872 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:43:49] "[33mGET /api/dashboard-data HTTP/1.1[0m" 404 -
2025-05-26 19:43:49,873 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:43:49] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-26 19:44:11,164 - tradingeconomics_scraper - INFO - Starting Trading Economics data generator for Forex Price Action Analysis System
2025-05-26 19:44:11,164 - tradingeconomics_scraper - INFO - Generating interest rates data...
2025-05-26 19:44:11,187 - tradingeconomics_scraper - INFO - Saved interest rates data to /home/<USER>/forex_web_app_local/data/economic_indicators/interest_rates.csv
2025-05-26 19:44:11,187 - tradingeconomics_scraper - INFO - Generating GDP data...
2025-05-26 19:44:11,188 - tradingeconomics_scraper - INFO - Saved GDP data to /home/<USER>/forex_web_app_local/data/economic_indicators/gdp_data.csv
2025-05-26 19:44:11,188 - tradingeconomics_scraper - INFO - Generating inflation data...
2025-05-26 19:44:11,192 - tradingeconomics_scraper - INFO - Saved inflation data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,192 - tradingeconomics_scraper - INFO - Generating PMI data...
2025-05-26 19:44:11,194 - tradingeconomics_scraper - INFO - Saved PMI data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,194 - tradingeconomics_scraper - INFO - Generating labor market data...
2025-05-26 19:44:11,199 - tradingeconomics_scraper - INFO - Saved labor market data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,199 - tradingeconomics_scraper - INFO - Generating retail sales data...
2025-05-26 19:44:11,200 - tradingeconomics_scraper - INFO - Saved retail sales data to /home/<USER>/forex_web_app_local/data/economic_indicators/retail_sales.csv
2025-05-26 19:44:11,200 - tradingeconomics_scraper - INFO - Generating consumer confidence data...
2025-05-26 19:44:11,200 - tradingeconomics_scraper - INFO - Saved consumer confidence data to /home/<USER>/forex_web_app_local/data/economic_indicators/consumer_confidence.csv
2025-05-26 19:44:11,201 - tradingeconomics_scraper - INFO - Generating US housing data...
2025-05-26 19:44:11,203 - tradingeconomics_scraper - INFO - Saved US housing data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,203 - tradingeconomics_scraper - INFO - Generating US Treasury yield curve data...
2025-05-26 19:44:11,204 - tradingeconomics_scraper - INFO - Saved US Treasury yield curve data to /home/<USER>/forex_web_app_local/data/economic_indicators/us_treasury_yield_curve.csv
2025-05-26 19:44:11,204 - tradingeconomics_scraper - INFO - Generating Fear/Greed index data...
2025-05-26 19:44:11,205 - tradingeconomics_scraper - INFO - Saved Fear/Greed index data to /home/<USER>/forex_web_app_local/data/economic_indicators/fear_greed_index.csv
2025-05-26 19:44:11,205 - tradingeconomics_scraper - INFO - Generating retail sentiment data...
2025-05-26 19:44:11,206 - tradingeconomics_scraper - INFO - Saved retail sentiment data to /home/<USER>/forex_web_app_local/data/economic_indicators/retail_sentiment.csv
2025-05-26 19:44:11,206 - tradingeconomics_scraper - INFO - Generating seasonality data...
2025-05-26 19:44:11,208 - tradingeconomics_scraper - INFO - Saved seasonality data to /home/<USER>/forex_web_app_local/data/economic_indicators/seasonality_data.csv
2025-05-26 19:44:11,208 - tradingeconomics_scraper - INFO - Generating COT data...
2025-05-26 19:44:11,209 - tradingeconomics_scraper - INFO - Saved COT data to /home/<USER>/forex_web_app_local/data/economic_indicators/cot_data.csv
2025-05-26 19:44:11,209 - tradingeconomics_scraper - INFO - Generating business forecast data...
2025-05-26 19:44:11,211 - tradingeconomics_scraper - INFO - Saved business forecast data to /home/<USER>/forex_web_app_local/data/economic_indicators/business_forecast.csv
2025-05-26 19:44:11,211 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-26 19:44:11,212 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 19:44:11,212 - tradingeconomics_scraper - INFO - Trading Economics data generator completed successfully
2025-05-26 19:44:11,305 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:44:11] "POST /api/collect-data HTTP/1.1" 200 -
2025-05-26 19:44:11,329 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:44:11] "GET /api/dashboard-data HTTP/1.1" 200 -
2025-05-26 20:24:32,353 - __main__ - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-26 20:24:32,361 - __main__ - INFO - Starting to scrape all countries
2025-05-26 20:24:32,361 - __main__ - INFO - Scraping indicators for switzerland
2025-05-26 20:24:34,418 - __main__ - INFO - Successfully scraped 19 indicators for switzerland
2025-05-26 20:24:38,203 - __main__ - INFO - Scraping indicators for australia
2025-05-26 20:24:40,137 - __main__ - INFO - Successfully scraped 21 indicators for australia
2025-05-26 20:24:42,629 - __main__ - INFO - Scraping indicators for new-zealand
2025-05-26 20:24:45,210 - __main__ - INFO - Successfully scraped 20 indicators for new-zealand
2025-05-26 20:24:47,952 - __main__ - INFO - Scraping indicators for united-kingdom
2025-05-26 20:24:49,966 - __main__ - INFO - Successfully scraped 20 indicators for united-kingdom
2025-05-26 20:24:52,971 - __main__ - INFO - Scraping indicators for canada
2025-05-26 20:24:54,670 - __main__ - INFO - Successfully scraped 21 indicators for canada
2025-05-26 20:24:58,034 - __main__ - INFO - Scraping indicators for euro-area
2025-05-26 20:24:59,538 - __main__ - INFO - Successfully scraped 20 indicators for euro-area
2025-05-26 20:25:03,176 - __main__ - INFO - Scraping indicators for united-states
2025-05-26 20:25:04,869 - __main__ - INFO - Successfully scraped 23 indicators for united-states
2025-05-26 20:25:07,006 - __main__ - INFO - Scraping indicators for japan
2025-05-26 20:25:09,241 - __main__ - INFO - Successfully scraped 21 indicators for japan
2025-05-26 20:25:13,900 - __main__ - INFO - Completed scraping 8 countries
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for EUR/USD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for USD/JPY
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for GBP/USD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for USD/CHF
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for USD/CAD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for AUD/USD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for NZD/USD
2025-05-26 20:25:13,902 - __main__ - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-26 20:25:13,902 - __main__ - INFO - Creating consolidated dashboard data...
2025-05-26 20:25:13,903 - __main__ - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 20:25:13,903 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-26 20:30:07,818 - __main__ - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-26 20:30:07,819 - __main__ - INFO - Starting to scrape all countries
2025-05-26 20:30:07,819 - __main__ - INFO - Scraping indicators for united-states
2025-05-26 20:30:09,772 - __main__ - INFO - Successfully scraped 23 indicators for united-states
2025-05-26 20:30:09,782 - __main__ - INFO - Saved united-states indicators to cache
2025-05-26 20:30:11,863 - __main__ - INFO - Scraping indicators for new-zealand
2025-05-26 20:30:14,198 - __main__ - INFO - Successfully scraped 20 indicators for new-zealand
2025-05-26 20:30:14,199 - __main__ - INFO - Saved new-zealand indicators to cache
2025-05-26 20:30:18,318 - __main__ - INFO - Scraping indicators for australia
2025-05-26 20:30:21,157 - __main__ - INFO - Successfully scraped 21 indicators for australia
2025-05-26 20:30:21,158 - __main__ - INFO - Saved australia indicators to cache
2025-05-26 20:30:23,816 - __main__ - INFO - Scraping indicators for japan
2025-05-26 20:30:26,338 - __main__ - INFO - Successfully scraped 21 indicators for japan
2025-05-26 20:30:26,339 - __main__ - INFO - Saved japan indicators to cache
2025-05-26 20:30:30,190 - __main__ - INFO - Scraping indicators for canada
2025-05-26 20:30:32,632 - __main__ - INFO - Successfully scraped 21 indicators for canada
2025-05-26 20:30:32,633 - __main__ - INFO - Saved canada indicators to cache
2025-05-26 20:30:34,656 - __main__ - INFO - Scraping indicators for united-kingdom
2025-05-26 20:30:37,101 - __main__ - INFO - Successfully scraped 20 indicators for united-kingdom
2025-05-26 20:30:37,101 - __main__ - INFO - Saved united-kingdom indicators to cache
2025-05-26 20:30:40,986 - __main__ - INFO - Scraping indicators for euro-area
2025-05-26 20:30:43,106 - __main__ - INFO - Successfully scraped 20 indicators for euro-area
2025-05-26 20:30:43,107 - __main__ - INFO - Saved euro-area indicators to cache
2025-05-26 20:30:46,100 - __main__ - INFO - Scraping indicators for switzerland
2025-05-26 20:30:47,843 - __main__ - INFO - Successfully scraped 19 indicators for switzerland
2025-05-26 20:30:47,843 - __main__ - INFO - Saved switzerland indicators to cache
2025-05-26 20:30:52,628 - __main__ - INFO - Completed scraping 8 countries
2025-05-26 20:30:52,628 - __main__ - INFO - Generating analysis for EUR/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for USD/JPY
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for GBP/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for USD/CHF
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for USD/CAD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for AUD/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for NZD/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-26 20:30:52,630 - __main__ - INFO - Updated history with new data points
2025-05-26 20:30:52,630 - __main__ - INFO - Creating consolidated dashboard data...
2025-05-26 20:30:52,632 - __main__ - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 20:30:52,632 - __main__ - INFO - Trading Economics scraper completed successfully
