2025-05-26 19:43:14,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-26 19:43:14,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-26 19:43:14,231 - werkzeug - INFO -  * Restarting with stat
2025-05-26 19:43:15,241 - werkzeug - WARNING -  * Debugger is active!
2025-05-26 19:43:15,242 - werkzeug - INFO -  * Debugger PIN: 192-990-882
2025-05-26 19:43:49,229 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:43:49] "GET / HTTP/1.1" 200 -
2025-05-26 19:43:49,872 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:43:49] "[33mGET /api/dashboard-data HTTP/1.1[0m" 404 -
2025-05-26 19:43:49,873 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:43:49] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-26 19:44:11,164 - tradingeconomics_scraper - INFO - Starting Trading Economics data generator for Forex Price Action Analysis System
2025-05-26 19:44:11,164 - tradingeconomics_scraper - INFO - Generating interest rates data...
2025-05-26 19:44:11,187 - tradingeconomics_scraper - INFO - Saved interest rates data to /home/<USER>/forex_web_app_local/data/economic_indicators/interest_rates.csv
2025-05-26 19:44:11,187 - tradingeconomics_scraper - INFO - Generating GDP data...
2025-05-26 19:44:11,188 - tradingeconomics_scraper - INFO - Saved GDP data to /home/<USER>/forex_web_app_local/data/economic_indicators/gdp_data.csv
2025-05-26 19:44:11,188 - tradingeconomics_scraper - INFO - Generating inflation data...
2025-05-26 19:44:11,192 - tradingeconomics_scraper - INFO - Saved inflation data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,192 - tradingeconomics_scraper - INFO - Generating PMI data...
2025-05-26 19:44:11,194 - tradingeconomics_scraper - INFO - Saved PMI data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,194 - tradingeconomics_scraper - INFO - Generating labor market data...
2025-05-26 19:44:11,199 - tradingeconomics_scraper - INFO - Saved labor market data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,199 - tradingeconomics_scraper - INFO - Generating retail sales data...
2025-05-26 19:44:11,200 - tradingeconomics_scraper - INFO - Saved retail sales data to /home/<USER>/forex_web_app_local/data/economic_indicators/retail_sales.csv
2025-05-26 19:44:11,200 - tradingeconomics_scraper - INFO - Generating consumer confidence data...
2025-05-26 19:44:11,200 - tradingeconomics_scraper - INFO - Saved consumer confidence data to /home/<USER>/forex_web_app_local/data/economic_indicators/consumer_confidence.csv
2025-05-26 19:44:11,201 - tradingeconomics_scraper - INFO - Generating US housing data...
2025-05-26 19:44:11,203 - tradingeconomics_scraper - INFO - Saved US housing data to /home/<USER>/forex_web_app_local/data/economic_indicators
2025-05-26 19:44:11,203 - tradingeconomics_scraper - INFO - Generating US Treasury yield curve data...
2025-05-26 19:44:11,204 - tradingeconomics_scraper - INFO - Saved US Treasury yield curve data to /home/<USER>/forex_web_app_local/data/economic_indicators/us_treasury_yield_curve.csv
2025-05-26 19:44:11,204 - tradingeconomics_scraper - INFO - Generating Fear/Greed index data...
2025-05-26 19:44:11,205 - tradingeconomics_scraper - INFO - Saved Fear/Greed index data to /home/<USER>/forex_web_app_local/data/economic_indicators/fear_greed_index.csv
2025-05-26 19:44:11,205 - tradingeconomics_scraper - INFO - Generating retail sentiment data...
2025-05-26 19:44:11,206 - tradingeconomics_scraper - INFO - Saved retail sentiment data to /home/<USER>/forex_web_app_local/data/economic_indicators/retail_sentiment.csv
2025-05-26 19:44:11,206 - tradingeconomics_scraper - INFO - Generating seasonality data...
2025-05-26 19:44:11,208 - tradingeconomics_scraper - INFO - Saved seasonality data to /home/<USER>/forex_web_app_local/data/economic_indicators/seasonality_data.csv
2025-05-26 19:44:11,208 - tradingeconomics_scraper - INFO - Generating COT data...
2025-05-26 19:44:11,209 - tradingeconomics_scraper - INFO - Saved COT data to /home/<USER>/forex_web_app_local/data/economic_indicators/cot_data.csv
2025-05-26 19:44:11,209 - tradingeconomics_scraper - INFO - Generating business forecast data...
2025-05-26 19:44:11,211 - tradingeconomics_scraper - INFO - Saved business forecast data to /home/<USER>/forex_web_app_local/data/economic_indicators/business_forecast.csv
2025-05-26 19:44:11,211 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-26 19:44:11,212 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 19:44:11,212 - tradingeconomics_scraper - INFO - Trading Economics data generator completed successfully
2025-05-26 19:44:11,305 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:44:11] "POST /api/collect-data HTTP/1.1" 200 -
2025-05-26 19:44:11,329 - werkzeug - INFO - 127.0.0.1 - - [26/May/2025 19:44:11] "GET /api/dashboard-data HTTP/1.1" 200 -
2025-05-26 20:24:32,353 - __main__ - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-26 20:24:32,361 - __main__ - INFO - Starting to scrape all countries
2025-05-26 20:24:32,361 - __main__ - INFO - Scraping indicators for switzerland
2025-05-26 20:24:34,418 - __main__ - INFO - Successfully scraped 19 indicators for switzerland
2025-05-26 20:24:38,203 - __main__ - INFO - Scraping indicators for australia
2025-05-26 20:24:40,137 - __main__ - INFO - Successfully scraped 21 indicators for australia
2025-05-26 20:24:42,629 - __main__ - INFO - Scraping indicators for new-zealand
2025-05-26 20:24:45,210 - __main__ - INFO - Successfully scraped 20 indicators for new-zealand
2025-05-26 20:24:47,952 - __main__ - INFO - Scraping indicators for united-kingdom
2025-05-26 20:24:49,966 - __main__ - INFO - Successfully scraped 20 indicators for united-kingdom
2025-05-26 20:24:52,971 - __main__ - INFO - Scraping indicators for canada
2025-05-26 20:24:54,670 - __main__ - INFO - Successfully scraped 21 indicators for canada
2025-05-26 20:24:58,034 - __main__ - INFO - Scraping indicators for euro-area
2025-05-26 20:24:59,538 - __main__ - INFO - Successfully scraped 20 indicators for euro-area
2025-05-26 20:25:03,176 - __main__ - INFO - Scraping indicators for united-states
2025-05-26 20:25:04,869 - __main__ - INFO - Successfully scraped 23 indicators for united-states
2025-05-26 20:25:07,006 - __main__ - INFO - Scraping indicators for japan
2025-05-26 20:25:09,241 - __main__ - INFO - Successfully scraped 21 indicators for japan
2025-05-26 20:25:13,900 - __main__ - INFO - Completed scraping 8 countries
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for EUR/USD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for USD/JPY
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for GBP/USD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for USD/CHF
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for USD/CAD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for AUD/USD
2025-05-26 20:25:13,901 - __main__ - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-26 20:25:13,901 - __main__ - INFO - Generating analysis for NZD/USD
2025-05-26 20:25:13,902 - __main__ - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-26 20:25:13,902 - __main__ - INFO - Creating consolidated dashboard data...
2025-05-26 20:25:13,903 - __main__ - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 20:25:13,903 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-26 20:30:07,818 - __main__ - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-26 20:30:07,819 - __main__ - INFO - Starting to scrape all countries
2025-05-26 20:30:07,819 - __main__ - INFO - Scraping indicators for united-states
2025-05-26 20:30:09,772 - __main__ - INFO - Successfully scraped 23 indicators for united-states
2025-05-26 20:30:09,782 - __main__ - INFO - Saved united-states indicators to cache
2025-05-26 20:30:11,863 - __main__ - INFO - Scraping indicators for new-zealand
2025-05-26 20:30:14,198 - __main__ - INFO - Successfully scraped 20 indicators for new-zealand
2025-05-26 20:30:14,199 - __main__ - INFO - Saved new-zealand indicators to cache
2025-05-26 20:30:18,318 - __main__ - INFO - Scraping indicators for australia
2025-05-26 20:30:21,157 - __main__ - INFO - Successfully scraped 21 indicators for australia
2025-05-26 20:30:21,158 - __main__ - INFO - Saved australia indicators to cache
2025-05-26 20:30:23,816 - __main__ - INFO - Scraping indicators for japan
2025-05-26 20:30:26,338 - __main__ - INFO - Successfully scraped 21 indicators for japan
2025-05-26 20:30:26,339 - __main__ - INFO - Saved japan indicators to cache
2025-05-26 20:30:30,190 - __main__ - INFO - Scraping indicators for canada
2025-05-26 20:30:32,632 - __main__ - INFO - Successfully scraped 21 indicators for canada
2025-05-26 20:30:32,633 - __main__ - INFO - Saved canada indicators to cache
2025-05-26 20:30:34,656 - __main__ - INFO - Scraping indicators for united-kingdom
2025-05-26 20:30:37,101 - __main__ - INFO - Successfully scraped 20 indicators for united-kingdom
2025-05-26 20:30:37,101 - __main__ - INFO - Saved united-kingdom indicators to cache
2025-05-26 20:30:40,986 - __main__ - INFO - Scraping indicators for euro-area
2025-05-26 20:30:43,106 - __main__ - INFO - Successfully scraped 20 indicators for euro-area
2025-05-26 20:30:43,107 - __main__ - INFO - Saved euro-area indicators to cache
2025-05-26 20:30:46,100 - __main__ - INFO - Scraping indicators for switzerland
2025-05-26 20:30:47,843 - __main__ - INFO - Successfully scraped 19 indicators for switzerland
2025-05-26 20:30:47,843 - __main__ - INFO - Saved switzerland indicators to cache
2025-05-26 20:30:52,628 - __main__ - INFO - Completed scraping 8 countries
2025-05-26 20:30:52,628 - __main__ - INFO - Generating analysis for EUR/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for USD/JPY
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for GBP/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for USD/CHF
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for USD/CAD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for AUD/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-26 20:30:52,629 - __main__ - INFO - Generating analysis for NZD/USD
2025-05-26 20:30:52,629 - __main__ - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-26 20:30:52,630 - __main__ - INFO - Updated history with new data points
2025-05-26 20:30:52,630 - __main__ - INFO - Creating consolidated dashboard data...
2025-05-26 20:30:52,632 - __main__ - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 20:30:52,632 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-27 18:14:39,199 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-27 18:14:39,199 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:14:42,313 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:14:42,315 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:42] "GET / HTTP/1.1" 200 -
2025-05-27 18:14:42,541 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:14:42,775 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:42] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-27 18:14:47,473 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:14:52,771 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:52] "GET /status HTTP/1.1" 200 -
2025-05-27 18:14:53,038 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:53] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-27 18:14:55,639 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:55] "POST /collect-data HTTP/1.1" 200 -
2025-05-27 18:14:55,644 - app - INFO - Starting background data collection
2025-05-27 18:14:55,644 - src.main - INFO - Starting Forex Price Action Analysis System
2025-05-27 18:14:55,644 - src.main - INFO - Running Trading Economics scraper...
2025-05-27 18:14:55,646 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-27 18:14:55,646 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-27 18:14:55,647 - tradingeconomics_scraper - INFO - Scraping indicators for new-zealand
2025-05-27 18:14:57,767 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:14:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:14:58,151 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for new-zealand
2025-05-27 18:14:58,153 - tradingeconomics_scraper - INFO - Saved new-zealand indicators to cache
2025-05-27 18:15:01,989 - tradingeconomics_scraper - INFO - Scraping indicators for united-states
2025-05-27 18:15:02,462 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:02] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:05,992 - tradingeconomics_scraper - INFO - Successfully scraped 22 indicators for united-states
2025-05-27 18:15:05,994 - tradingeconomics_scraper - INFO - Saved united-states indicators to cache
2025-05-27 18:15:07,775 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:07] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:08,647 - tradingeconomics_scraper - INFO - Scraping indicators for australia
2025-05-27 18:15:12,459 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:12] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:12,680 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for australia
2025-05-27 18:15:12,682 - tradingeconomics_scraper - INFO - Saved australia indicators to cache
2025-05-27 18:15:17,413 - tradingeconomics_scraper - INFO - Scraping indicators for japan
2025-05-27 18:15:17,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:21,251 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for japan
2025-05-27 18:15:21,253 - tradingeconomics_scraper - INFO - Saved japan indicators to cache
2025-05-27 18:15:22,460 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:22] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:23,870 - tradingeconomics_scraper - INFO - Scraping indicators for canada
2025-05-27 18:15:26,136 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for canada
2025-05-27 18:15:26,138 - tradingeconomics_scraper - INFO - Saved canada indicators to cache
2025-05-27 18:15:27,767 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:28,360 - tradingeconomics_scraper - INFO - Scraping indicators for united-kingdom
2025-05-27 18:15:31,505 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for united-kingdom
2025-05-27 18:15:31,507 - tradingeconomics_scraper - INFO - Saved united-kingdom indicators to cache
2025-05-27 18:15:32,464 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:32] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:34,035 - tradingeconomics_scraper - INFO - Scraping indicators for euro-area
2025-05-27 18:15:37,542 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for euro-area
2025-05-27 18:15:37,544 - tradingeconomics_scraper - INFO - Saved euro-area indicators to cache
2025-05-27 18:15:37,780 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:41,191 - tradingeconomics_scraper - INFO - Scraping indicators for switzerland
2025-05-27 18:15:42,462 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:44,431 - tradingeconomics_scraper - INFO - Successfully scraped 19 indicators for switzerland
2025-05-27 18:15:44,433 - tradingeconomics_scraper - INFO - Saved switzerland indicators to cache
2025-05-27 18:15:47,770 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:48,661 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-27 18:15:48,661 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-27 18:15:48,661 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-27 18:15:48,662 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-27 18:15:48,662 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-27 18:15:48,662 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-27 18:15:48,662 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-27 18:15:48,663 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-27 18:15:48,663 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-27 18:15:48,663 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-27 18:15:48,663 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-27 18:15:48,663 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-27 18:15:48,664 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-27 18:15:48,664 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-27 18:15:48,664 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-27 18:15:48,667 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-27 18:15:48,667 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-27 18:15:48,677 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to D:\Projects\Forex\forex_system_final_v2\local\data\dashboard_data.json
2025-05-27 18:15:48,678 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-27 18:15:48,678 - src.main - INFO - Trading Economics scraper completed successfully
2025-05-27 18:15:48,678 - src.main - INFO - Updating dashboard...
2025-05-27 18:15:48,693 - dashboard_updater - INFO - Starting dashboard updater for Forex Price Action Analysis System
2025-05-27 18:15:48,694 - dashboard_updater - INFO - Updating dashboard HTML for local
2025-05-27 18:15:48,695 - dashboard_updater - INFO - Loaded dashboard data from D:\Projects\Forex\forex_system_final_v2\local\data\dashboard_data.json
2025-05-27 18:15:48,697 - dashboard_updater - INFO - Dashboard HTML updated successfully: D:\Projects\Forex\forex_system_final_v2\local\data\dashboard.html
2025-05-27 18:15:48,697 - dashboard_updater - INFO - Dashboard updater completed successfully
2025-05-27 18:15:48,697 - src.main - INFO - Dashboard updated successfully
2025-05-27 18:15:48,698 - src.main - INFO - Copying dashboard HTML to root directory...
2025-05-27 18:15:48,699 - src.main - INFO - Dashboard HTML copied to D:\Projects\Forex\forex_system_final_v2\local\src\..\dashboard.html
2025-05-27 18:15:48,700 - src.main - INFO - Exporting data to CSV files...
2025-05-27 18:15:48,701 - src.main - ERROR - Error exporting data: [Errno 2] No such file or directory: 'D:\\Projects\\Forex\\forex_system_final_v2\\local\\src\\..\\data\\exports\\EUR/USD_indicators.csv'
2025-05-27 18:15:48,702 - src.main - ERROR - Failed to export data to CSV files
2025-05-27 18:15:48,702 - src.main - INFO - Forex Price Action Analysis System completed successfully
2025-05-27 18:15:48,702 - app - INFO - Background data collection completed successfully
2025-05-27 18:15:52,459 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:52] "GET /status HTTP/1.1" 200 -
2025-05-27 18:15:57,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:15:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:02,466 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:02] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:07,782 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:16:07,783 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:07] "GET / HTTP/1.1" 200 -
2025-05-27 18:16:07,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:07] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:08,042 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:08] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-27 18:16:12,821 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:12] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:17,825 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:23,134 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:23] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:26,082 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:16:26,082 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:26] "GET / HTTP/1.1" 200 -
2025-05-27 18:16:26,398 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:26] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:26,411 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:26] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-27 18:16:27,162 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:16:27,162 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:27] "GET / HTTP/1.1" 200 -
2025-05-27 18:16:27,484 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:27,499 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:27] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-27 18:16:30,704 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:30] "GET /api/export-all-csv HTTP/1.1" 200 -
2025-05-27 18:16:33,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:33] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:37,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:43,180 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:43] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:47,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:53,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:53] "GET /status HTTP/1.1" 200 -
2025-05-27 18:16:57,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:16:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:03,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:03] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:07,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:07] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:13,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:13] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:17,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:23,196 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:23] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:27,205 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:32,511 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:32] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:37,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:43,200 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:43] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:47,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:53,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:53] "GET /status HTTP/1.1" 200 -
2025-05-27 18:17:57,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:17:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:03,188 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:03] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:07,871 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:07] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:13,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:13] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:17,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:23,191 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:23] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:27,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:33,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:33] "GET /status HTTP/1.1" 200 -
2025-05-27 18:18:56,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:18:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:21,721 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:21] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:22,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:22] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:28,186 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:28] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:32,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:32] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:38,195 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:38] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:42,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:48,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:48] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:52,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:52] "GET /status HTTP/1.1" 200 -
2025-05-27 18:19:58,180 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:19:58] "GET /status HTTP/1.1" 200 -
2025-05-27 18:20:02,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:02] "GET /status HTTP/1.1" 200 -
2025-05-27 18:20:08,186 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:08] "GET /status HTTP/1.1" 200 -
2025-05-27 18:20:12,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:12] "GET /status HTTP/1.1" 200 -
2025-05-27 18:20:18,195 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:18] "GET /status HTTP/1.1" 200 -
2025-05-27 18:20:56,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:20:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:21:57,190 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:21:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:22:56,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:22:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:23:57,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:23:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:05,267 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:05] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:07,499 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:07] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:12,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:12] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:18,194 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:18] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:22,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:22] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:28,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:28] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:32,875 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:32] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:38,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:38] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:42,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:47,504 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:52,210 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:52] "GET /status HTTP/1.1" 200 -
2025-05-27 18:24:56,342 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:24:56,343 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:56] "GET / HTTP/1.1" 200 -
2025-05-27 18:24:56,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:24:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:01,363 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:01] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:07,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:07] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:11,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:11] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:17,191 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:21,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:21] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:27,188 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:31,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:31] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:37,198 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:41,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:41] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:47,190 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:51,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:51] "GET /status HTTP/1.1" 200 -
2025-05-27 18:25:57,191 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:25:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:26:56,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:26:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:27:57,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:27:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:03,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:03] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:05,374 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:28:05,374 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:05] "GET / HTTP/1.1" 200 -
2025-05-27 18:28:05,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:05] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:10,417 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:10] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:10,905 - app - INFO - Starting background data collection
2025-05-27 18:28:10,906 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:10] "POST /collect-data HTTP/1.1" 200 -
2025-05-27 18:28:10,906 - src.main - INFO - Starting Forex Price Action Analysis System
2025-05-27 18:28:10,907 - src.main - INFO - Running Trading Economics scraper...
2025-05-27 18:28:10,908 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-27 18:28:10,908 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-27 18:28:10,910 - tradingeconomics_scraper - INFO - Using cached data for new-zealand
2025-05-27 18:28:10,912 - tradingeconomics_scraper - INFO - Loaded new-zealand indicators from cache
2025-05-27 18:28:14,048 - tradingeconomics_scraper - INFO - Using cached data for united-states
2025-05-27 18:28:14,049 - tradingeconomics_scraper - INFO - Loaded united-states indicators from cache
2025-05-27 18:28:15,418 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:15] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:18,767 - tradingeconomics_scraper - INFO - Using cached data for australia
2025-05-27 18:28:18,768 - tradingeconomics_scraper - INFO - Loaded australia indicators from cache
2025-05-27 18:28:20,738 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:20] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:21,746 - tradingeconomics_scraper - INFO - Using cached data for japan
2025-05-27 18:28:21,747 - tradingeconomics_scraper - INFO - Loaded japan indicators from cache
2025-05-27 18:28:24,839 - tradingeconomics_scraper - INFO - Using cached data for canada
2025-05-27 18:28:24,840 - tradingeconomics_scraper - INFO - Loaded canada indicators from cache
2025-05-27 18:28:25,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:25] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:28,615 - tradingeconomics_scraper - INFO - Using cached data for united-kingdom
2025-05-27 18:28:28,617 - tradingeconomics_scraper - INFO - Loaded united-kingdom indicators from cache
2025-05-27 18:28:30,729 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:30] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:31,916 - tradingeconomics_scraper - INFO - Using cached data for euro-area
2025-05-27 18:28:31,917 - tradingeconomics_scraper - INFO - Loaded euro-area indicators from cache
2025-05-27 18:28:34,048 - tradingeconomics_scraper - INFO - Using cached data for switzerland
2025-05-27 18:28:34,049 - tradingeconomics_scraper - INFO - Loaded switzerland indicators from cache
2025-05-27 18:28:35,416 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:35] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:36,850 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-27 18:28:36,851 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-27 18:28:36,851 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-27 18:28:36,852 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-27 18:28:36,853 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-27 18:28:36,853 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-27 18:28:36,853 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-27 18:28:36,854 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-27 18:28:36,854 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-27 18:28:36,854 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-27 18:28:36,855 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-27 18:28:36,855 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-27 18:28:36,855 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-27 18:28:36,855 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-27 18:28:36,855 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-27 18:28:36,859 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-27 18:28:36,859 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-27 18:28:36,871 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to D:\Projects\Forex\forex_system_final_v2\local\data\dashboard_data.json
2025-05-27 18:28:36,871 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-27 18:28:36,871 - src.main - INFO - Trading Economics scraper completed successfully
2025-05-27 18:28:36,872 - src.main - INFO - Updating dashboard...
2025-05-27 18:28:36,872 - dashboard_updater - INFO - Starting dashboard updater for Forex Price Action Analysis System
2025-05-27 18:28:36,872 - dashboard_updater - INFO - Updating dashboard HTML for local
2025-05-27 18:28:36,873 - dashboard_updater - INFO - Loaded dashboard data from D:\Projects\Forex\forex_system_final_v2\local\data\dashboard_data.json
2025-05-27 18:28:36,875 - dashboard_updater - INFO - Dashboard HTML updated successfully: D:\Projects\Forex\forex_system_final_v2\local\data\dashboard.html
2025-05-27 18:28:36,875 - dashboard_updater - INFO - Dashboard updater completed successfully
2025-05-27 18:28:36,876 - src.main - INFO - Dashboard updated successfully
2025-05-27 18:28:36,876 - src.main - INFO - Copying dashboard HTML to root directory...
2025-05-27 18:28:36,878 - src.main - INFO - Dashboard HTML copied to D:\Projects\Forex\forex_system_final_v2\local\src\..\dashboard.html
2025-05-27 18:28:36,878 - src.main - INFO - Exporting data to CSV files...
2025-05-27 18:28:36,880 - src.main - ERROR - Error exporting data: [Errno 2] No such file or directory: 'D:\\Projects\\Forex\\forex_system_final_v2\\local\\src\\..\\data\\exports\\EUR/USD_indicators.csv'
2025-05-27 18:28:36,880 - src.main - ERROR - Failed to export data to CSV files
2025-05-27 18:28:36,880 - src.main - INFO - Forex Price Action Analysis System completed successfully
2025-05-27 18:28:36,880 - app - INFO - Background data collection completed successfully
2025-05-27 18:28:40,735 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:40] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:45,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:45] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:47,409 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:28:47,411 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:47] "GET / HTTP/1.1" 200 -
2025-05-27 18:28:47,654 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:48,238 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:28:48,239 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:48] "GET / HTTP/1.1" 200 -
2025-05-27 18:28:48,567 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:48] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:48,817 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:28:48,818 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:48] "GET / HTTP/1.1" 200 -
2025-05-27 18:28:48,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:48] "GET /status HTTP/1.1" 200 -
2025-05-27 18:28:49,915 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:28:49] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:29:20,243 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:29:20] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:31:01,061 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-27 18:31:01,061 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:31:04,123 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:31:04,124 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:31:04,145 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:31:04,146 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:04] "GET / HTTP/1.1" 200 -
2025-05-27 18:31:04,387 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:04] "GET /status HTTP/1.1" 200 -
2025-05-27 18:31:08,977 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:31:08,978 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:31:08,978 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:31:08,979 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:08] "GET / HTTP/1.1" 200 -
2025-05-27 18:31:09,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:09] "GET /status HTTP/1.1" 200 -
2025-05-27 18:31:10,576 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:10] "GET /dashboard-data HTTP/1.1" 200 -
2025-05-27 18:31:15,388 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:31:15,388 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:31:15,389 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:31:15,390 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:15] "GET / HTTP/1.1" 200 -
2025-05-27 18:31:15,654 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:15] "GET /status HTTP/1.1" 200 -
2025-05-27 18:31:17,328 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:31:17,329 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:31:17,329 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:31:17,330 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:17] "GET / HTTP/1.1" 200 -
2025-05-27 18:31:17,652 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:31:22,349 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:22] "GET /status HTTP/1.1" 200 -
2025-05-27 18:31:24,968 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:24] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-27 18:31:27,342 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:31:27,824 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:27] "[35m[1mGET /health HTTP/1.1[0m" 500 -
2025-05-27 18:31:30,593 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:31:30] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:32:01,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:01] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:32:31,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:31] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:32:51,224 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:32:51,225 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:32:51,225 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:32:51,225 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:51] "GET / HTTP/1.1" 200 -
2025-05-27 18:32:51,490 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:51] "GET /status HTTP/1.1" 200 -
2025-05-27 18:32:56,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:32:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:02,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:02] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:06,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:06] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:12,186 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:12] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:16,871 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:16] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:22,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:22] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:26,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:26] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:32,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:32] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:36,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:36] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:41,556 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:33:41,557 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:33:41,557 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:33:41,557 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:41] "GET / HTTP/1.1" 200 -
2025-05-27 18:33:41,820 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:41] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:43,419 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:33:43,419 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:33:43,420 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:33:43,420 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:43] "GET / HTTP/1.1" 200 -
2025-05-27 18:33:43,738 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:43] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:48,439 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:48] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:50,812 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:50] "POST /collect-data HTTP/1.1" 200 -
2025-05-27 18:33:50,819 - app - INFO - Starting background data collection
2025-05-27 18:33:50,820 - src.main - INFO - Starting Forex Price Action Analysis System
2025-05-27 18:33:50,820 - src.main - INFO - Running Trading Economics scraper...
2025-05-27 18:33:50,822 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-27 18:33:50,822 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-27 18:33:50,823 - tradingeconomics_scraper - INFO - Using cached data for united-states
2025-05-27 18:33:50,824 - tradingeconomics_scraper - INFO - Loaded united-states indicators from cache
2025-05-27 18:33:53,434 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:53] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:55,198 - tradingeconomics_scraper - INFO - Using cached data for australia
2025-05-27 18:33:55,199 - tradingeconomics_scraper - INFO - Loaded australia indicators from cache
2025-05-27 18:33:58,748 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:33:58] "GET /status HTTP/1.1" 200 -
2025-05-27 18:33:59,027 - tradingeconomics_scraper - INFO - Using cached data for japan
2025-05-27 18:33:59,027 - tradingeconomics_scraper - INFO - Loaded japan indicators from cache
2025-05-27 18:34:01,316 - tradingeconomics_scraper - INFO - Using cached data for euro-area
2025-05-27 18:34:01,317 - tradingeconomics_scraper - INFO - Loaded euro-area indicators from cache
2025-05-27 18:34:03,439 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:03] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:03,696 - tradingeconomics_scraper - INFO - Using cached data for switzerland
2025-05-27 18:34:03,698 - tradingeconomics_scraper - INFO - Loaded switzerland indicators from cache
2025-05-27 18:34:06,406 - tradingeconomics_scraper - INFO - Using cached data for canada
2025-05-27 18:34:06,407 - tradingeconomics_scraper - INFO - Loaded canada indicators from cache
2025-05-27 18:34:08,758 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:08] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:10,102 - tradingeconomics_scraper - INFO - Using cached data for united-kingdom
2025-05-27 18:34:10,103 - tradingeconomics_scraper - INFO - Loaded united-kingdom indicators from cache
2025-05-27 18:34:13,434 - tradingeconomics_scraper - INFO - Using cached data for new-zealand
2025-05-27 18:34:13,436 - tradingeconomics_scraper - INFO - Loaded new-zealand indicators from cache
2025-05-27 18:34:13,438 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:13] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:16,004 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-27 18:34:16,005 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-27 18:34:16,005 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-27 18:34:16,005 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-27 18:34:16,005 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-27 18:34:16,005 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-27 18:34:16,005 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-27 18:34:16,006 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-27 18:34:16,006 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-27 18:34:16,006 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-27 18:34:16,007 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-27 18:34:16,007 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-27 18:34:16,007 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-27 18:34:16,007 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-27 18:34:16,007 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-27 18:34:16,010 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-27 18:34:16,010 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-27 18:34:16,020 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to D:\Projects\Forex\forex_system_final_v2\local\data\dashboard_data.json
2025-05-27 18:34:16,020 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-27 18:34:16,020 - src.main - INFO - Trading Economics scraper completed successfully
2025-05-27 18:34:16,021 - src.main - INFO - Dashboard update skipped - using Flask web interface
2025-05-27 18:34:16,021 - src.main - INFO - Dashboard copy skipped - using Flask web interface
2025-05-27 18:34:16,021 - src.main - INFO - Exporting data to CSV files...
2025-05-27 18:34:16,022 - src.main - ERROR - Error exporting data: [Errno 2] No such file or directory: 'D:\\Projects\\Forex\\forex_system_final_v2\\local\\src\\..\\data\\exports\\EUR/USD_indicators.csv'
2025-05-27 18:34:16,023 - src.main - ERROR - Failed to export data to CSV files
2025-05-27 18:34:16,023 - src.main - INFO - Forex Price Action Analysis System completed successfully
2025-05-27 18:34:16,023 - app - INFO - Background data collection completed successfully
2025-05-27 18:34:18,755 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:18] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:20,766 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:20,767 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:20,767 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:20,769 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:20] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:21,088 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:21] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:23,115 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:23,116 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:23,116 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:23,116 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:23] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:23,452 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:23] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:25,027 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:25,028 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:25,028 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:25,028 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:25] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:25,345 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:25] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:26,240 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:26,240 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:26,241 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:26,241 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:26] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:26,559 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:26] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:26,852 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:26,853 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:26,853 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:26,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:26] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:27,181 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:27,187 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:27,188 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:27,189 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:27] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:27,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:27,552 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:27,552 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:27,552 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:27,552 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:27] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:27,566 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:28,107 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:34:28,108 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:34:28,108 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:34:28,108 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:28] "GET / HTTP/1.1" 200 -
2025-05-27 18:34:28,373 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:28] "GET /status HTTP/1.1" 200 -
2025-05-27 18:34:30,019 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:34:30] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:35:01,210 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:01] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:35:47,104 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-27 18:35:47,104 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:35:50,171 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:35:50,172 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:35:50,193 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:35:50,194 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:50] "GET / HTTP/1.1" 200 -
2025-05-27 18:35:50,438 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:50] "GET /status HTTP/1.1" 200 -
2025-05-27 18:35:55,273 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:35:55] "GET /status HTTP/1.1" 200 -
2025-05-27 18:36:00,583 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:00] "GET /status HTTP/1.1" 200 -
2025-05-27 18:36:04,016 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:36:04,017 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:36:04,017 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:36:04,018 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:04] "GET / HTTP/1.1" 200 -
2025-05-27 18:36:04,342 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:04] "GET /status HTTP/1.1" 200 -
2025-05-27 18:36:09,037 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:09] "GET /status HTTP/1.1" 200 -
2025-05-27 18:36:11,621 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:11] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:36:42,203 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:36:42] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:37:12,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:37:12] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:37:44,186 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:37:44] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:38:14,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:14] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:38:46,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:38:46] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:39:16,895 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:39:16] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:39:48,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:39:48] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:40:18,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:40:18] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:40:50,188 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:40:50] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:41:20,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:41:20] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:41:52,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:41:52] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:42:22,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:42:22] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:42:54,195 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:42:54] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:43:24,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:43:24] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:43:56,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:43:56] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:44:26,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:44:26] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:44:58,194 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:44:58] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:45:28,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:45:28] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:46:00,198 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:00] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:46:30,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:46:30] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:47:02,184 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:47:02] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:47:32,894 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:47:32] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:48:17,360 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-27 18:48:17,360 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:48:20,438 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:48:20,439 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:48:20,439 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:48:20,439 - app - INFO - Data timestamp: 2025-05-27 18:34:16
2025-05-27 18:48:20,459 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:48:20,460 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:20] "GET / HTTP/1.1" 200 -
2025-05-27 18:48:20,702 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:20] "GET /status HTTP/1.1" 200 -
2025-05-27 18:48:23,925 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:48:23,925 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:48:23,925 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:48:23,925 - app - INFO - Data timestamp: 2025-05-27 18:34:16
2025-05-27 18:48:23,926 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:48:23,927 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:23] "GET / HTTP/1.1" 200 -
2025-05-27 18:48:24,246 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:24] "GET /status HTTP/1.1" 200 -
2025-05-27 18:48:27,587 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:27] "GET /dashboard-data HTTP/1.1" 200 -
2025-05-27 18:48:37,500 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:48:37,501 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:48:37,501 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:48:37,501 - app - INFO - Data timestamp: 2025-05-27 18:34:16
2025-05-27 18:48:37,502 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:48:37,502 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:37] "GET / HTTP/1.1" 200 -
2025-05-27 18:48:37,763 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:48:38,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:48:38] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:49:08,556 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:08] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:49:11,422 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:11] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:49:14,597 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:49:14,597 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:49:14,598 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:49:14,598 - app - INFO - Data timestamp: 2025-05-27 18:34:16
2025-05-27 18:49:14,599 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:49:14,600 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:14] "GET / HTTP/1.1" 200 -
2025-05-27 18:49:14,856 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:14] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:17,305 - app - INFO - Starting background data collection
2025-05-27 18:49:17,305 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:17] "POST /collect-data HTTP/1.1" 200 -
2025-05-27 18:49:17,306 - src.tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-27 18:49:17,307 - src.tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-27 18:49:17,308 - src.tradingeconomics_scraper - INFO - Using cached data for euro-area
2025-05-27 18:49:17,309 - src.tradingeconomics_scraper - INFO - Loaded euro-area indicators from cache
2025-05-27 18:49:19,922 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:19] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:20,670 - src.tradingeconomics_scraper - INFO - Using cached data for australia
2025-05-27 18:49:20,672 - src.tradingeconomics_scraper - INFO - Loaded australia indicators from cache
2025-05-27 18:49:24,146 - src.tradingeconomics_scraper - INFO - Using cached data for switzerland
2025-05-27 18:49:24,147 - src.tradingeconomics_scraper - INFO - Loaded switzerland indicators from cache
2025-05-27 18:49:24,616 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:24] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:28,298 - src.tradingeconomics_scraper - INFO - Using cached data for canada
2025-05-27 18:49:28,299 - src.tradingeconomics_scraper - INFO - Loaded canada indicators from cache
2025-05-27 18:49:30,182 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:30] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:31,027 - src.tradingeconomics_scraper - INFO - Using cached data for united-kingdom
2025-05-27 18:49:31,028 - src.tradingeconomics_scraper - INFO - Loaded united-kingdom indicators from cache
2025-05-27 18:49:34,013 - src.tradingeconomics_scraper - INFO - Using cached data for united-states
2025-05-27 18:49:34,014 - src.tradingeconomics_scraper - INFO - Loaded united-states indicators from cache
2025-05-27 18:49:34,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:34] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:37,488 - src.tradingeconomics_scraper - INFO - Using cached data for new-zealand
2025-05-27 18:49:37,490 - src.tradingeconomics_scraper - INFO - Loaded new-zealand indicators from cache
2025-05-27 18:49:40,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:40] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:40,251 - src.tradingeconomics_scraper - INFO - Using cached data for japan
2025-05-27 18:49:40,252 - src.tradingeconomics_scraper - INFO - Loaded japan indicators from cache
2025-05-27 18:49:42,542 - src.tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-27 18:49:42,543 - src.tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-27 18:49:42,543 - src.tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-27 18:49:42,544 - src.tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-27 18:49:42,544 - src.tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-27 18:49:42,544 - src.tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-27 18:49:42,544 - src.tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-27 18:49:42,544 - src.tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-27 18:49:42,545 - src.tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-27 18:49:42,546 - src.tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-27 18:49:42,546 - src.tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-27 18:49:42,546 - src.tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-27 18:49:42,547 - src.tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-27 18:49:42,547 - src.tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-27 18:49:42,547 - src.tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-27 18:49:42,549 - src.tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-27 18:49:42,549 - src.tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-27 18:49:42,559 - src.tradingeconomics_scraper - INFO - Saved consolidated dashboard data to D:\Projects\Forex\forex_system_final_v2\local\data\dashboard_data.json
2025-05-27 18:49:42,559 - src.tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-27 18:49:42,559 - app - INFO - Background data collection completed successfully
2025-05-27 18:49:44,871 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:44] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:48,179 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:49:48,180 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:49:48,180 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:49:48,180 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:49:48,181 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:49:48,182 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:48] "GET / HTTP/1.1" 200 -
2025-05-27 18:49:48,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:48] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:50,874 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:49:50,874 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:49:50,874 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:49:50,875 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:49:50,875 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:49:50,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:50] "GET / HTTP/1.1" 200 -
2025-05-27 18:49:51,210 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:51] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:53,222 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:49:53,222 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:49:53,223 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:49:53,223 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:49:53,223 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:49:53,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:53] "GET / HTTP/1.1" 200 -
2025-05-27 18:49:53,553 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:53] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:55,574 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:49:55,575 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:49:55,575 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:49:55,576 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:49:55,576 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:49:55,576 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:55] "GET / HTTP/1.1" 200 -
2025-05-27 18:49:55,908 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:55] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:57,931 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:49:57,931 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:49:57,932 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:49:57,932 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:49:57,933 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:49:57,933 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:57] "GET / HTTP/1.1" 200 -
2025-05-27 18:49:58,249 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:58] "GET /status HTTP/1.1" 200 -
2025-05-27 18:49:59,514 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:49:59,514 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:49:59,515 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:49:59,515 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:49:59,515 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:49:59,515 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:59] "GET / HTTP/1.1" 200 -
2025-05-27 18:49:59,837 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:49:59] "GET /status HTTP/1.1" 200 -
2025-05-27 18:50:00,184 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:50:00,185 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:50:00,185 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:50:00,185 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:50:00,185 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:50:00,186 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:00] "GET / HTTP/1.1" 200 -
2025-05-27 18:50:00,499 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:50:00,500 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:50:00,500 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:50:00,500 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:50:00,500 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:50:00,501 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:00] "GET / HTTP/1.1" 200 -
2025-05-27 18:50:00,592 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:00] "GET /status HTTP/1.1" 200 -
2025-05-27 18:50:02,617 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:50:02,617 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:50:02,617 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:50:02,617 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:50:02,617 - app - ERROR - Error loading dashboard: 'float' object is not iterable
2025-05-27 18:50:02,618 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:02] "GET / HTTP/1.1" 200 -
2025-05-27 18:50:02,633 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:02] "GET /status HTTP/1.1" 200 -
2025-05-27 18:50:03,651 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:03] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:50:33,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:50:33] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:51:04,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:51:04] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:51:34,249 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:51:34] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:52:05,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:52:05] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:55:13,533 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-27 18:55:13,534 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 18:55:16,475 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:55:16,476 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:55:16,476 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:55:16,476 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:55:16,483 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:16] "GET / HTTP/1.1" 200 -
2025-05-27 18:55:16,738 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:16] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:21,567 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:21] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:26,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:26] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:31,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:31] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:36,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:36] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:41,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:41] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:46,855 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:46] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:51,552 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:51] "GET /status HTTP/1.1" 200 -
2025-05-27 18:55:56,870 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:55:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:01,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:01] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:06,864 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:06] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:11,562 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:11] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:17,195 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:21,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:21] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:27,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:31,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:31] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:37,187 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:41,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:41] "GET /status HTTP/1.1" 200 -
2025-05-27 18:56:45,845 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:56:45] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:57:08,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:08] "[35m[1mGET /health HTTP/1.1[0m" 500 -
2025-05-27 18:57:16,172 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:16] "GET /logs HTTP/1.1" 200 -
2025-05-27 18:57:17,753 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:57:17,753 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:57:17,754 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:57:17,754 - app - INFO - Data timestamp: 2025-05-27 18:49:42
2025-05-27 18:57:17,755 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:17] "GET / HTTP/1.1" 200 -
2025-05-27 18:57:18,084 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:18] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:22,770 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:22] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:28,094 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:28] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:32,779 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:32] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:34,787 - app - INFO - Starting background data collection
2025-05-27 18:57:34,787 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:34] "POST /collect-data HTTP/1.1" 200 -
2025-05-27 18:57:34,788 - src.tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-27 18:57:34,788 - src.tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-27 18:57:34,788 - src.tradingeconomics_scraper - INFO - Using cached data for united-kingdom
2025-05-27 18:57:34,789 - src.tradingeconomics_scraper - INFO - Loaded united-kingdom indicators from cache
2025-05-27 18:57:37,113 - src.tradingeconomics_scraper - INFO - Using cached data for australia
2025-05-27 18:57:37,113 - src.tradingeconomics_scraper - INFO - Loaded australia indicators from cache
2025-05-27 18:57:37,772 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:40,039 - src.tradingeconomics_scraper - INFO - Using cached data for euro-area
2025-05-27 18:57:40,040 - src.tradingeconomics_scraper - INFO - Loaded euro-area indicators from cache
2025-05-27 18:57:43,090 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:43] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:44,103 - src.tradingeconomics_scraper - INFO - Using cached data for switzerland
2025-05-27 18:57:44,104 - src.tradingeconomics_scraper - INFO - Loaded switzerland indicators from cache
2025-05-27 18:57:46,707 - src.tradingeconomics_scraper - INFO - Using cached data for new-zealand
2025-05-27 18:57:46,708 - src.tradingeconomics_scraper - INFO - Loaded new-zealand indicators from cache
2025-05-27 18:57:47,772 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:51,383 - src.tradingeconomics_scraper - INFO - Using cached data for united-states
2025-05-27 18:57:51,384 - src.tradingeconomics_scraper - INFO - Loaded united-states indicators from cache
2025-05-27 18:57:53,082 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:53] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:55,757 - src.tradingeconomics_scraper - INFO - Using cached data for canada
2025-05-27 18:57:55,759 - src.tradingeconomics_scraper - INFO - Loaded canada indicators from cache
2025-05-27 18:57:57,770 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:57:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:57:57,903 - src.tradingeconomics_scraper - INFO - Using cached data for japan
2025-05-27 18:57:57,903 - src.tradingeconomics_scraper - INFO - Loaded japan indicators from cache
2025-05-27 18:58:01,248 - src.tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-27 18:58:01,249 - src.tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-27 18:58:01,249 - src.tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-27 18:58:01,249 - src.tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-27 18:58:01,249 - src.tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-27 18:58:01,249 - src.tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-27 18:58:01,250 - src.tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-27 18:58:01,250 - src.tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-27 18:58:01,250 - src.tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-27 18:58:01,250 - src.tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-27 18:58:01,250 - src.tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-27 18:58:01,250 - src.tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-27 18:58:01,251 - src.tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-27 18:58:01,251 - src.tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-27 18:58:01,251 - src.tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-27 18:58:01,252 - src.tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-27 18:58:01,252 - src.tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-27 18:58:01,255 - src.tradingeconomics_scraper - INFO - Saved consolidated dashboard data to D:\Projects\Forex\forex_system_final_v2\local\data\dashboard_data.json
2025-05-27 18:58:01,255 - src.tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-27 18:58:01,255 - app - INFO - Background data collection completed successfully
2025-05-27 18:58:03,080 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:03] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:05,093 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:05,093 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:05,093 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:05,093 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:05,093 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:05] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:05,415 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:05] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:07,446 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:07,446 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:07,446 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:07,446 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:07,447 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:07] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:07,776 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:07] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:09,793 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:09,794 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:09,794 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:09,795 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:09,796 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:09] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:10,121 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:10] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:12,133 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:12,134 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:12,134 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:12,134 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:12,135 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:12] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:12,452 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:12] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:14,467 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:14,467 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:14,467 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:14,467 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:14,468 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:14] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:14,788 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:14] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:16,809 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:16,809 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:16,809 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:16,809 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:16,810 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:16] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:17,131 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:19,150 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:19,150 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:19,150 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:19,151 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:19,151 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:19] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:19,467 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:19] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:21,481 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:21,481 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:21,481 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:21,481 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:21,482 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:21] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:21,809 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:21] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:23,824 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:23,824 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:23,824 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:23,824 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:23,825 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:23] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:24,154 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:24] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:26,165 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:26,166 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:26,166 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:26,167 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:26,167 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:26] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:26,498 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:26] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:28,526 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:28,526 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:28,526 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:28,526 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:28,528 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:28] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:28,844 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:28] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:30,870 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:30,870 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:30,870 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:30,871 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:30,871 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:30] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:31,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:31] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:33,232 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:33,232 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:33,232 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:33,232 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:33,233 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:33] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:33,562 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:33] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:35,360 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:35] "[33mGET /export/EUR/USD HTTP/1.1[0m" 404 -
2025-05-27 18:58:36,185 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:36,185 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:36,186 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:36,186 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:36,186 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:36] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:36,450 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:36] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:37,426 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:37,426 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:37,427 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:37,428 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:37,429 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:37] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:37,754 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:38,868 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:38] "GET /api/export-all-csv HTTP/1.1" 200 -
2025-05-27 18:58:39,188 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:39,188 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:39,188 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:39,189 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:39,189 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:39] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:39,455 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:39] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:39,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:39,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:39,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:39,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:39,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:39] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:40,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:40] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:41,880 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:41,880 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:41,880 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:41,880 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:41,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:41] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:42,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:42,465 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:42,465 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:42,465 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:42,465 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:42,466 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:42] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:42,527 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:44,538 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:44,538 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:44,538 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:44,538 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:44,539 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:44] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:44,865 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:44] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:45,129 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:45,129 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:45,130 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:45,130 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:45,131 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:45] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:45,176 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:45] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:46,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:46,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:46,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:46,883 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:46,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:46] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:47,218 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:47] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:47,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:47,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:47,890 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:47,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:47,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:47] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:48,227 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:48] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:49,910 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:49,910 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:49,910 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:49,911 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:49,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:49] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:50,239 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:50] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:50,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:50,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:50,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:50,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:50,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:50] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:51,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:51] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:52,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:52,885 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:52,885 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:52,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:52,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:52] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:53,225 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:53] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:53,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:53,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:53,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:53,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:53,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:53] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:54,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:54] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:55,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:55,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:55,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:55,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:55,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:55] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:56,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:56,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:56,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:56,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:56,889 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:56,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:56] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:57,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:58,883 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:58,883 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:58,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:58,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:58,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:58] "GET / HTTP/1.1" 200 -
2025-05-27 18:58:59,217 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:59] "GET /status HTTP/1.1" 200 -
2025-05-27 18:58:59,890 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:58:59,891 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:58:59,891 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:58:59,891 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:58:59,892 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:58:59] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:00,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:00] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:01,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:01,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:01,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:01,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:01,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:01] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:02,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:02] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:02,877 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:02,878 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:02,878 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:02,878 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:02,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:02] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:03,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:03] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:04,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:04,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:04,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:04,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:04,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:04] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:05,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:05] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:05,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:05,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:05,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:05,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:05,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:05] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:06,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:06] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:07,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:07,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:07,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:07,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:07,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:07] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:08,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:08] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:08,883 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:08,883 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:08,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:08,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:08,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:08] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:09,218 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:09] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:10,877 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:10,878 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:10,878 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:10,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:10,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:10] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:11,214 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:11] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:11,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:11,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:11,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:11,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:11,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:11] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:12,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:12] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:13,881 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:13,881 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:13,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:13,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:13,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:13] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:14,214 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:14] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:14,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:14,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:14,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:14,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:14,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:14] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:15,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:15] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:16,891 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:16,891 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:16,892 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:16,892 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:16,892 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:16] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:17,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:17] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:17,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:17,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:17,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:17,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:17,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:17] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:18,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:18] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:19,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:19,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:19,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:19,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:19,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:19] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:20,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:20] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:20,894 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:20,894 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:20,894 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:20,895 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:20,895 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:20] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:21,227 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:21] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:22,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:22,885 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:22,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:22,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:22,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:22] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:23,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:23] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:23,891 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:23,891 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:23,891 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:23,891 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:23,892 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:23] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:24,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:24] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:25,238 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:25,238 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:25,238 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:25,238 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:25,239 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:25] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:25,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:25] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:26,875 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:26,875 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:26,875 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:26,875 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:26,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:26] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:27,199 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:27,587 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:27,587 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:27,588 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:27,588 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:27,588 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:27] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:27,917 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:27] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:29,875 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:29,875 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:29,875 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:29,875 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:29,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:29] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:30,200 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:30,200 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:30,200 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:30,201 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:30,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:30] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:30,230 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:30] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:30,465 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:30] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:32,494 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:32,494 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:32,495 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:32,495 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:32,496 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:32] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:32,822 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:32] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:33,086 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:33,086 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:33,087 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:33,087 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:33,088 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:33] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:33,195 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:33] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:34,835 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:34,835 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:34,835 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:34,835 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:34,836 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:34] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:35,156 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:35] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:35,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:35,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:35,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:35,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:35,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:35] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:36,209 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:36] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:37,181 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:37,181 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:37,182 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:37,182 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:37,183 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:37] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:37,512 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:37] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:38,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:38,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:38,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:38,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:38,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:38] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:39,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:39] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:39,532 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:39,532 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:39,532 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:39,532 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:39,533 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:39] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:39,848 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:39] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:41,874 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:41,874 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:41,874 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:41,875 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:41,875 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:41] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:42,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:42,193 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:42,193 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:42,193 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:42,193 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:42,194 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:42] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:42,455 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:42] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:44,225 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:44,225 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:44,226 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:44,226 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:44,227 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:44] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:44,557 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:44] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:44,878 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:44,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:44,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:44,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:44,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:44] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:45,206 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:45] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:46,574 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:46,574 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:46,575 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:46,575 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:46,575 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:46] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:46,897 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:46] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:47,890 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:47,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:47,891 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:47,891 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:47,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:47] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:48,213 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:48] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:48,914 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:48,915 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:48,915 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:48,915 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:48,915 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:48] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:49,235 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:49] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:50,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:50,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:50,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:50,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:50,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:50] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:51,217 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:51] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:51,468 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:51,469 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:51,469 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:51,469 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:51,469 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:51] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:51,560 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:51] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:53,575 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:53,575 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:53,575 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:53,575 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:53,576 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:53] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:53,906 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:53,906 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:53,906 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:53,906 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:53,907 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:53] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:54,157 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:54] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:54,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:54] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:56,173 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:56,173 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:56,173 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:56,173 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:56,174 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:56] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:56,490 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:56] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:56,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:56,885 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:56,885 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:56,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:56,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:56] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:57,206 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:57] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:58,516 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:58,517 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:58,517 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:58,517 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:58,517 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:58] "GET / HTTP/1.1" 200 -
2025-05-27 18:59:58,846 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:58] "GET /status HTTP/1.1" 200 -
2025-05-27 18:59:59,890 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 18:59:59,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 18:59:59,890 - app - INFO - Rendering dashboard with data: True
2025-05-27 18:59:59,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 18:59:59,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 18:59:59] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:00,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:00] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:00,873 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:00,874 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:00,874 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:00,874 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:00,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:00] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:01,208 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:01] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:02,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:02,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:02,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:02,889 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:02,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:02] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:03,211 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:03] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:03,475 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:03,475 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:03,475 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:03,475 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:03,476 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:03] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:03,521 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:03] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:05,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:05,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:05,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:05,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:05,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:05] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:06,206 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:06,206 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:06,207 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:06,207 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:06,207 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:06] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:06,211 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:06] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:06,546 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:06] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:08,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:08,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:08,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:08,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:08,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:08] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:09,200 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:09,200 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:09,200 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:09,200 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:09,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:09] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:09,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:09] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:09,545 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:09] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:11,883 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:11,883 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:11,883 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:11,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:11,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:11] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:12,197 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:12,197 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:12,197 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:12,198 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:12,199 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:12] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:12,210 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:12] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:12,540 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:12] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:14,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:14,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:14,896 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:14,896 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:14,897 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:14] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:15,202 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:15,203 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:15,203 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:15,203 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:15,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:15] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:15,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:15] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:15,542 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:15] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:18,192 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:18,193 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:18,193 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:18,193 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:18,194 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:18] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:18,455 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:18,455 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:18,455 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:18,455 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:18,456 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:18] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:18,500 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:18] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:18,531 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:18] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:20,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:20,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:20,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:20,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:20,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:20] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:21,193 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:21,194 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:21,194 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:21,195 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:21,196 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:21] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:21,209 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:21] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:21,537 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:21] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:23,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:23,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:23,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:23,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:23,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:23] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:24,202 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:24,203 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:24,203 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:24,203 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:24,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:24] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:24,218 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:24] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:24,532 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:24] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:26,893 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:26,893 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:26,894 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:26,894 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:26,894 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:26] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:27,199 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:27,199 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:27,199 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:27,199 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:27,199 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:27] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:27,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:27] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:27,541 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:27] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:29,865 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:29,866 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:29,866 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:29,866 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:29,866 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:29] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:30,132 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:30,132 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:30,132 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:30,132 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:30,133 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:30] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:30,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:30] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:30,194 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:30] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:30,707 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:30] "GET /api/export-all-csv HTTP/1.1" 200 -
2025-05-27 19:00:32,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:32,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:32,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:32,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:32,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:32] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:33,201 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:33,201 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:33,202 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:33,202 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:33,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:33] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:33,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:33] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:33,543 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:33] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:35,229 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:35,229 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:35,229 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:35,229 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:35,230 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:35] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:35,551 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:35] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:35,878 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:35,878 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:35,878 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:35,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:35,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:35] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:36,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:36] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:37,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:37,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:37,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:37,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:37,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:37] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:38,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:38] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:38,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:38,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:38,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:38,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:38,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:38] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:39,217 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:39] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:40,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:40,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:40,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:40,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:40,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:40] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:41,210 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:41] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:41,877 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:41,878 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:41,878 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:41,878 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:41,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:41] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:42,211 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:42] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:43,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:43,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:43,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:43,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:43,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:43] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:44,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:44] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:44,877 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:44,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:44,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:44,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:44,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:44] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:45,212 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:45] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:46,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:46,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:46,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:46,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:46,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:46] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:47,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:47] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:47,890 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:47,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:47,890 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:47,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:47,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:47] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:48,225 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:48] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:49,883 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:49,883 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:49,883 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:49,883 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:49,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:49] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:50,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:50] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:50,878 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:50,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:50,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:50,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:50,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:50] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:51,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:51] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:52,883 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:52,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:52,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:52,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:52,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:52] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:53,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:53] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:53,892 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:53,892 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:53,893 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:53,893 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:53,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:53] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:54,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:54] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:55,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:55,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:55,883 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:55,883 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:55,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:55] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:56,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:56] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:56,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:56,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:56,890 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:56,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:56,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:56] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:57,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:57] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:58,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:58,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:58,898 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:58,898 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:58,899 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:58] "GET / HTTP/1.1" 200 -
2025-05-27 19:00:59,229 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:59] "GET /status HTTP/1.1" 200 -
2025-05-27 19:00:59,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:00:59,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:00:59,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:00:59,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:00:59,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:00:59] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:00,227 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:00] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:01,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:01,885 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:01,885 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:01,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:01,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:01] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:02,217 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:02] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:02,890 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:02,891 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:02,891 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:02,891 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:02,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:02] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:03,229 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:03] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:04,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:04,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:04,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:04,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:04,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:04] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:05,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:05] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:05,878 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:05,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:05,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:05,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:05,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:05] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:06,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:06] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:07,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:07,885 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:07,885 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:07,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:07,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:07] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:08,214 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:08] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:08,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:08,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:08,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:08,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:08,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:08] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:09,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:09] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:10,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:10,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:10,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:10,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:10,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:10] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:11,217 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:11] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:11,880 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:11,880 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:11,881 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:11,881 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:11,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:11] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:12,211 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:12] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:13,243 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:13,243 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:13,243 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:13,243 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:13,244 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:13] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:13,574 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:13] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:13,991 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:13] "GET /logs HTTP/1.1" 200 -
2025-05-27 19:01:15,191 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:15,191 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:15,192 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:15,192 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:15,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:15] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:15,455 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:15] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:17,880 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:17,880 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:17,881 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:17,881 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:17,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:17] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:18,196 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:18] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:20,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:20,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:20,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:20,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:20,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:20] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:21,210 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:21] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:23,878 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:23,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:23,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:23,880 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:23,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:23] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:24,199 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:24] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:26,879 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:26,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:26,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:26,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:26,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:26] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:27,199 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:27] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:29,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:29,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:29,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:29,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:29,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:29] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:30,208 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:30] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:32,877 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:32,877 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:32,878 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:32,878 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:32,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:32] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:33,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:33] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:35,875 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:35,875 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:35,876 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:35,876 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:35,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:35] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:36,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:36] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:38,880 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:38,881 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:38,881 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:38,881 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:38,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:38] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:39,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:39] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:41,877 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:41,878 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:41,878 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:41,878 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:41,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:41] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:42,205 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:42] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:44,015 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:44] "GET /logs HTTP/1.1" 200 -
2025-05-27 19:01:45,184 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:45,184 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:45,184 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:45,184 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:45,185 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:45] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:45,447 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:45] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:47,880 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:47,880 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:47,880 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:47,881 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:47,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:47] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:48,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:48] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:50,877 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:50,877 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:50,878 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:50,878 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:50,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:50] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:51,214 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:51] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:53,878 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:53,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:53,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:53,879 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:53,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:53] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:54,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:54] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:56,880 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:56,880 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:56,880 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:56,881 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:56,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:56] "GET / HTTP/1.1" 200 -
2025-05-27 19:01:57,214 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:57] "GET /status HTTP/1.1" 200 -
2025-05-27 19:01:59,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:01:59,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:01:59,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:01:59,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:01:59,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:01:59] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:00,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:00] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:02,876 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:02,876 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:02,877 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:02,877 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:02,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:02] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:03,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:03] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:05,881 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:05,881 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:05,881 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:05,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:05,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:05] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:06,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:06] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:08,879 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:08,879 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:08,879 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:08,880 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:08,880 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:08] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:09,210 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:09] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:09,956 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:09,956 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:09,957 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:09,957 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:09,958 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:09] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:10,278 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:10] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:11,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:11,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:11,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:11,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:11,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:11] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:12,206 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:12] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:12,467 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:12,468 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:12,468 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:12,468 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:12,469 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:12] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:12,621 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:12] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:14,637 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:14,637 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:14,637 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:14,637 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:14,638 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:14] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:14,968 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:14,968 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:14,968 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:14,969 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:14,970 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:14] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:15,184 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:15] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:15,215 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:15] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:17,196 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:17,197 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:17,197 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:17,197 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:17,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:17] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:17,518 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:17] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:17,876 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:17,876 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:17,876 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:17,876 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:17,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:17] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:18,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:18] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:19,533 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:19,533 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:19,533 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:19,534 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:19,536 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:19] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:19,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:19] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:20,890 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:20,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:20,890 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:20,891 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:20,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:20] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:21,227 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:21] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:21,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:21,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:21,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:21,889 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:21,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:21] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:22,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:22] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:23,891 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:23,891 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:23,892 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:23,892 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:23,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:23] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:24,227 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:24] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:24,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:24,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:24,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:24,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:24,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:24] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:25,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:25] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:26,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:26,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:26,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:26,889 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:26,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:26] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:27,225 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:27] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:27,897 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:27,898 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:27,898 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:27,899 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:27,900 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:27] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:28,231 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:28] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:29,883 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:29,883 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:29,883 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:29,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:29,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:29] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:30,218 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:30] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:30,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:30,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:30,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:30,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:30,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:30] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:31,217 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:31] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:32,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:32,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:32,886 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:32,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:32,887 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:32] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:33,212 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:33] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:33,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:33,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:33,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:33,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:33,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:33] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:34,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:34] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:35,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:35,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:35,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:35,889 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:35,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:35] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:36,233 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:36] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:36,879 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:36,880 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:36,880 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:36,880 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:36,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:36] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:37,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:37] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:38,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:38,885 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:38,885 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:38,886 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:38,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:38] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:39,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:39] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:39,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:39,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:39,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:39,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:39,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:39] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:40,218 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:40] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:41,885 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:41,885 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:41,885 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:41,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:41,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:41] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:42,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:42] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:42,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:42,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:42,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:42,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:42,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:42] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:43,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:43] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:44,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:44,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:44,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:44,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:44,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:44] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:45,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:45] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:45,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:45,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:45,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:45,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:45,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:45] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:46,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:46] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:47,876 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:47,876 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:47,877 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:47,877 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:47,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:47] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:48,212 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:48] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:48,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:48,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:48,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:48,889 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:48,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:48] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:49,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:49] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:50,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:50,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:50,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:50,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:50,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:50] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:51,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:51] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:51,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:51,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:51,889 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:51,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:51,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:51] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:52,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:52] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:53,880 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:53,880 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:53,880 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:53,880 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:53,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:53] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:54,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:54] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:54,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:54,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:54,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:54,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:54,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:54] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:55,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:55] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:56,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:56,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:56,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:56,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:56,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:56] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:57,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:57] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:57,893 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:57,893 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:57,893 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:57,893 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:57,894 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:57] "GET / HTTP/1.1" 200 -
2025-05-27 19:02:58,234 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:58] "GET /status HTTP/1.1" 200 -
2025-05-27 19:02:59,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:02:59,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:02:59,883 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:02:59,883 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:02:59,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:02:59] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:00,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:00] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:00,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:00,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:00,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:00,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:00,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:00] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:01,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:01] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:02,892 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:02,892 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:02,892 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:02,893 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:02,893 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:02] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:03,227 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:03] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:03,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:03,889 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:03,890 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:03,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:03,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:03] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:04,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:04] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:05,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:05,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:05,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:05,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:05,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:05] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:06,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:06] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:06,888 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:06,888 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:06,888 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:06,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:06,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:06] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:07,218 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:07] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:08,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:08,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:08,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:08,885 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:08,885 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:08] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:09,221 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:09] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:09,889 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:09,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:09,890 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:09,890 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:09,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:09] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:10,219 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:10] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:11,882 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:11,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:11,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:11,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:11,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:11] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:12,200 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:12] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:12,466 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:12,467 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:12,467 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:12,468 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:12,469 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:12] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:12,541 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:12] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:13,655 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:13] "GET /logs HTTP/1.1" 200 -
2025-05-27 19:03:15,192 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:15,192 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:15,192 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:15,193 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:15,193 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:15] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:15,455 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:15] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:17,881 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:17,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:17,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:17,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:17,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:17] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:18,208 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:18] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:20,890 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:20,890 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:20,891 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:20,891 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:20,892 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:20] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:21,212 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:21] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:23,875 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:23,876 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:23,876 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:23,876 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:23,876 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:23] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:24,196 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:24] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:26,881 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:26,881 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:26,881 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:26,881 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:26,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:26] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:27,216 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:27] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:29,881 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:29,882 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:29,882 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:29,882 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:29,883 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:29] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:30,218 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:30] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:32,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:32,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:32,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:32,888 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:32,889 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:32] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:33,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:33] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:35,886 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:35,886 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:35,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:35,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:35,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:35] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:36,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:36] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:38,887 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:38,887 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:38,887 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:38,887 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:38,888 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:38] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:39,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:39] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:41,884 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:41,884 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:41,884 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:41,884 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:41,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:41] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:42,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:42] "GET /status HTTP/1.1" 200 -
2025-05-27 19:03:43,886 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:43] "GET /logs HTTP/1.1" 200 -
2025-05-27 19:03:45,188 - app - INFO - Dashboard data loaded: 7 pairs
2025-05-27 19:03:45,188 - app - INFO - Sample pair data - EUR/USD: Sell
2025-05-27 19:03:45,189 - app - INFO - Rendering dashboard with data: True
2025-05-27 19:03:45,189 - app - INFO - Data timestamp: 2025-05-27 18:58:01
2025-05-27 19:03:45,191 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:45] "GET / HTTP/1.1" 200 -
2025-05-27 19:03:45,447 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 19:03:45] "GET /status HTTP/1.1" 200 -
