# Forex System Final V2 - Technical Documentation

## Module Documentation

### 1. tradingeconomics_scraper.py

#### Purpose
Web scraper that fetches real economic indicators from TradingEconomics.com for major currency analysis.

#### Key Functions

**`scrape_country_indicators(country, app_type)`**
- Scrapes economic indicators for a specific country
- Implements intelligent caching with 1-hour TTL
- Returns dictionary of indicators with values, units, and dates

**`generate_pair_analysis(pair, country_data)`**
- Analyzes currency pair based on economic differentials
- Calculates sentiment score (-2 to +2 range)
- Generates trading signals and confidence levels

**`run_scraper(app_type='local')`**
- Main entry point for scraping process
- Orchestrates data collection for all countries
- Updates history and creates dashboard data

#### Data Structures

**Currency Pairs Configuration:**
```python
CURRENCY_PAIRS = {
    'EUR/USD': {'base': 'euro-area', 'quote': 'united-states'},
    'USD/JPY': {'base': 'united-states', 'quote': 'japan'},
    # ... additional pairs
}
```

**Indicator Categories:**
```python
INDICATORS = {
    'gdp_data': ['GDP Growth Rate', 'GDP Annual Growth Rate'],
    'employment_data': ['Unemployment Rate', 'Non Farm Payrolls'],
    'core_inflation': ['Core Inflation Rate'],
    # ... additional indicators
}
```

#### Caching Mechanism
- **Cache Duration**: 3600 seconds (1 hour)
- **Cache Location**: `data/cache/{country}_indicators.json`
- **Cache Validation**: Timestamp-based expiration
- **Fallback Strategy**: Use expired cache on scraping failure

#### Error Handling
- **Request Errors**: Automatic retry with cached data fallback
- **Parsing Errors**: Graceful degradation with logging
- **Rate Limiting**: Random delays (1-5 seconds) between requests

### 2. dashboard_updater.py

#### Purpose
Generates HTML dashboard from scraped economic data using template system.

#### Key Functions

**`load_dashboard_data(app_type)`**
- Loads consolidated dashboard data from JSON
- Validates data structure and handles missing files

**`generate_pair_html(pair, data)`**
- Creates HTML representation for currency pair
- Includes signal badges, indicators table, and history
- Applies CSS classes based on signal strength

**`update_dashboard_html(app_type)`**
- Main dashboard generation function
- Replaces template placeholders with dynamic content
- Outputs final HTML to `data/dashboard.html`

#### Template System
- **Template Location**: `src/templates/index.html`
- **Placeholders**: `{{CURRENCY_PAIRS}}`, `{{LAST_UPDATED}}`
- **Output**: `data/dashboard.html`

#### HTML Components
- **Signal Badges**: Color-coded trading signals
- **Indicators Table**: Top 10 impactful indicators
- **History Table**: Last 3 data points
- **Impact Bars**: Visual representation of indicator impact

### 3. main.py

#### Purpose
Main orchestrator that coordinates the entire data collection and dashboard update process.

#### Workflow
1. **Data Collection**: Runs TradingEconomics scraper
2. **Dashboard Update**: Generates HTML dashboard
3. **File Management**: Copies dashboard to root directory
4. **Data Export**: Creates CSV exports for analysis

#### Key Functions

**`run_scraper()`**
- Imports and executes scraper module
- Handles errors and provides status feedback

**`update_dashboard()`**
- Imports and executes dashboard updater
- Ensures data consistency between modules

**`export_data_to_csv()`**
- Exports dashboard data to CSV format
- Creates separate files for indicators and history

### 4. models/economic_data.py

#### Purpose
SQLAlchemy models for persistent data storage and historical tracking.

#### Models

**`EconomicIndicator`**
```python
class EconomicIndicator(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    indicator_type = db.Column(db.String(50), nullable=False)
    currency = db.Column(db.String(10), nullable=False)
    value = db.Column(db.Float, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
```

**`DashboardData`**
```python
class DashboardData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    pair = db.Column(db.String(10), nullable=False)
    score = db.Column(db.Float, nullable=False)
    signal = db.Column(db.String(20), nullable=False)
    confidence = db.Column(db.Float, nullable=False)
    optimal_horizon = db.Column(db.Integer, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
```

#### Query Methods
- **`get_latest_for_currency()`**: Retrieves recent indicator data
- **`get_latest_for_pair()`**: Gets recent dashboard snapshots

### 5. routes/api.py

#### Purpose
REST API endpoints for data collection, retrieval, and export functionality.

#### Endpoints

**`POST /api/collect-data`**
- Triggers complete data collection process
- Stores results in database for historical tracking
- Returns success status and timestamp

**`GET /api/dashboard-data`**
- Returns latest dashboard data with history
- Includes all currency pairs and indicators
- JSON format for frontend consumption

**`GET /api/indicator-history/<type>/<currency>`**
- Retrieves historical data for specific indicator
- Limited to last 3 data points
- Useful for trend analysis

**`GET /api/export-csv/<indicator>`**
- Exports specific indicator data as CSV
- Supports both file-based and database-generated exports
- Automatic filename with timestamp

**`GET /api/export-all-csv`**
- Creates ZIP archive of all indicator CSV files
- Bulk export functionality
- Timestamped archive names

#### Data Processing
- **Database Integration**: Automatic storage of collected data
- **Error Handling**: Comprehensive error responses
- **File Management**: Dynamic CSV generation and ZIP creation

## Algorithm Details

### Sentiment Score Calculation

The system calculates sentiment scores using weighted economic differentials:

```python
# Interest rate differential (weight: 0.4)
score += interest_rate_diff * 0.4

# Inflation differential (weight: 0.3, inverted)
score -= inflation_rate_diff * 0.3

# Growth differential (weight: 0.2)
score += gdp_growth_diff * 0.2

# Unemployment differential (weight: 0.1, inverted)
score -= unemployment_diff * 0.1
```

### Signal Generation

Signals are determined based on score thresholds:
- **Strong Buy**: score > 1.0
- **Buy**: score > 0.3
- **Neutral**: -0.3 ≤ score ≤ 0.3
- **Sell**: score < -0.3
- **Strong Sell**: score < -1.0

### Confidence Calculation

Confidence is based on data availability:
- Each available differential adds 20% confidence
- Maximum confidence: 100%
- Default confidence (no data): 50%

### Optimal Horizon Determination

Trading horizons based on signal strength:
- **Strong signals** (|score| > 1.5): 3 days
- **Moderate signals** (|score| > 0.8): 7 days
- **Weak signals** (|score| > 0.3): 15 days

## Performance Considerations

### Caching Strategy
- **Memory Usage**: JSON files for fast access
- **Disk Usage**: Automatic cleanup of old cache files
- **Network Usage**: Reduced API calls through intelligent caching

### Database Optimization
- **Indexing**: Automatic indexes on timestamp and currency fields
- **Query Optimization**: Limit clauses for historical data
- **Connection Pooling**: SQLAlchemy connection management

### Scalability
- **Horizontal Scaling**: Stateless design allows multiple instances
- **Vertical Scaling**: Efficient memory usage with streaming
- **Load Balancing**: API endpoints support load distribution

## Security Implementation

### Web Scraping Security
- **User-Agent Rotation**: Mimics legitimate browser behavior
- **Rate Limiting**: Respects server resources
- **Error Handling**: Prevents infinite retry loops

### Data Security
- **Input Validation**: Sanitizes all user inputs
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: HTML escaping in templates

### API Security
- **Error Handling**: No sensitive information in error messages
- **File Access**: Restricted to designated directories
- **Resource Limits**: Prevents excessive resource consumption

## Monitoring and Debugging

### Logging Configuration
```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("module_name.log"),
        logging.StreamHandler()
    ]
)
```

### Debug Information
- **Request/Response Logging**: Full HTTP transaction logs
- **Data Validation**: Automatic data quality checks
- **Performance Metrics**: Timing information for operations

### Error Recovery
- **Graceful Degradation**: System continues with partial data
- **Automatic Retry**: Built-in retry mechanisms
- **Fallback Data**: Cached data when live data unavailable
