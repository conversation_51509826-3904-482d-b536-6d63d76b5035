# Forex System Final V2 - Code Index

## System Overview
A comprehensive Forex Price Action Analysis System that scrapes real economic data from TradingEconomics.com and provides analysis for major currency pairs.

## Architecture

### Core Components
1. **Data Scraper** (`src/tradingeconomics_scraper.py`) - Web scrapes economic indicators
2. **Dashboard Updater** (`src/dashboard_updater.py`) - Generates HTML dashboard  
3. **Main Controller** (`src/main.py`) - Orchestrates the entire process
4. **Flask API** (`src/routes/api.py`) - REST API endpoints
5. **Database Models** (`src/models/`) - SQLAlchemy models for data persistence

### Currency Pairs Supported
- EUR/USD, USD/JPY, GBP/USD, USD/CHF, USD/CAD, AUD/USD, NZD/USD

### Economic Indicators Tracked
- GDP Growth Rate, Unemployment Rate, Inflation Rate, Interest Rates
- PMI (Manufacturing & Services), Consumer Confidence, Retail Sales
- Business Confidence, Housing Data, Producer Prices, PCE Index

## File Structure

```
├── src/
│   ├── main.py                    # Main entry point and orchestrator
│   ├── tradingeconomics_scraper.py # Web scraper for economic data
│   ├── dashboard_updater.py       # HTML dashboard generator
│   ├── models/
│   │   ├── economic_data.py       # Database models for indicators
│   │   └── user.py               # User management models
│   ├── routes/
│   │   ├── api.py                # REST API endpoints
│   │   └── user.py               # User management routes
│   ├── templates/
│   │   └── index.html            # Dashboard HTML template
│   └── static/
│       └── index.html            # Static HTML files
├── data/
│   ├── cache/                    # Cached economic data (1-hour TTL)
│   ├── economic_indicators/      # CSV exports of indicators
│   ├── exports/                  # Data exports for analysis
│   ├── dashboard_data.json       # Consolidated dashboard data
│   └── dashboard.html            # Generated dashboard
├── instance/
│   └── forex_data.db            # SQLite database
└── requirements.txt             # Python dependencies
```

## Key Features

### 1. Real-time Data Scraping
- Fetches live economic indicators from TradingEconomics.com
- Handles rate limiting with random delays
- Robust error handling and fallback to cached data

### 2. Intelligent Caching System
- 1-hour cache duration to avoid rate limiting
- Country-specific cache files in JSON format
- Automatic cache validation and expiration

### 3. Currency Pair Analysis
- Calculates sentiment scores based on economic differentials
- Generates trading signals (Strong Buy/Buy/Neutral/Sell/Strong Sell)
- Determines optimal trading horizons (3-15 days)
- Confidence scoring based on data availability

### 4. Historical Tracking
- Maintains 3-point history for trend analysis
- Stores data in both JSON cache and SQLite database
- Tracks score, signal, and confidence over time

### 5. Interactive Dashboard
- Web-based dashboard with Bootstrap styling
- Currency pair filtering and selection
- Real-time data collection trigger
- Export functionality (CSV/JSON)

### 6. REST API Endpoints
- `/api/collect-data` - Trigger data collection
- `/api/dashboard-data` - Get latest dashboard data
- `/api/indicator-history/<type>/<currency>` - Get indicator history
- `/api/export-csv/<indicator>` - Export specific indicator
- `/api/export-all-csv` - Export all data as ZIP

## Dependencies

### Core Dependencies
- **Flask 3.1.0** - Web framework
- **Flask-SQLAlchemy 3.1.1** - Database ORM
- **SQLAlchemy 2.0.40** - Database toolkit
- **requests 2.31.0** - HTTP library for web scraping
- **beautifulsoup4 4.12.2** - HTML parsing
- **pandas 2.1.4** - Data manipulation and CSV handling
- **lxml 4.9.3** - XML/HTML parser (BeautifulSoup backend)

### Database Dependencies
- **PyMySQL 1.1.1** - MySQL connector
- **cryptography 36.0.2** - Encryption support

## Data Flow

1. **Data Collection**: `main.py` → `tradingeconomics_scraper.py`
2. **Data Processing**: Economic indicators → Currency pair analysis
3. **Dashboard Generation**: `dashboard_updater.py` → HTML output
4. **Data Export**: CSV files → `data/exports/`
5. **API Access**: Flask routes → JSON responses

## Configuration

### Application Types
- **local** - Development environment
- **deployable** - Production environment

### Cache Settings
- **CACHE_DURATION**: 3600 seconds (1 hour)
- **CACHE_HISTORY_SIZE**: 3 data points
- **Rate Limiting**: 1-3 second delays between requests

## Usage

### Running the System
```bash
python src/main.py
```

### API Usage
```bash
# Trigger data collection
curl -X POST http://localhost:5000/api/collect-data

# Get dashboard data
curl http://localhost:5000/api/dashboard-data

# Export indicator data
curl http://localhost:5000/api/export-csv/gdp_data
```

## Error Handling

### Scraping Errors
- Automatic fallback to cached data
- Comprehensive logging to `tradingeconomics_scraper.log`
- Graceful degradation when data unavailable

### Dashboard Errors
- Template validation and error reporting
- Fallback to default values when data missing
- Logging to `dashboard_updater.log`

## Security Considerations

### Web Scraping
- User-Agent rotation to mimic browser behavior
- Rate limiting to respect server resources
- Error handling for blocked requests

### Data Storage
- SQLite database for development
- MySQL support for production
- Encrypted connections with cryptography library

## Performance Optimizations

### Caching Strategy
- Country-level caching reduces API calls
- JSON serialization for fast data access
- Automatic cache invalidation

### Database Optimization
- Indexed queries for historical data
- Efficient data models with proper relationships
- Batch operations for bulk data storage

## Monitoring and Logging

### Log Files
- `main.log` - Main application events
- `tradingeconomics_scraper.log` - Scraping activities
- `dashboard_updater.log` - Dashboard generation

### Data Validation
- Automatic data type validation
- Missing data detection and handling
- Confidence scoring based on data quality
