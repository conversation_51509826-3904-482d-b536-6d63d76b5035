#!/usr/bin/env python3
"""
Forex Price Action Analysis System - Standalone Web Application

This Flask application provides a complete web interface for the forex analysis system.
No command line interaction required - everything is accessible through the web interface.
"""

import os
import sys
import logging
from flask import Flask, render_template, jsonify, request, send_file, redirect, url_for, flash
from datetime import datetime
import json
import threading
import time

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import our modules
from src.models.economic_data import db, EconomicIndicator, DashboardData
from src.routes.api import api_bp
from src.routes.user import user_bp

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__,
                template_folder='src/templates',
                static_folder='src/static')

    # Configuration
    app.config['SECRET_KEY'] = 'forex-analysis-secret-key-change-in-production'

    # Use absolute path for database
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'instance', 'forex_data.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Ensure instance directory exists
    os.makedirs('instance', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('data/cache', exist_ok=True)
    os.makedirs('data/exports', exist_ok=True)
    os.makedirs('data/economic_indicators', exist_ok=True)

    # Initialize database
    db.init_app(app)

    # Register blueprints
    app.register_blueprint(api_bp)
    app.register_blueprint(user_bp)

    # Create database tables
    with app.app_context():
        db.create_all()

    return app

# Create the Flask app
app = create_app()

# Global variable to track data collection status
data_collection_status = {
    'running': False,
    'last_run': None,
    'last_success': None,
    'message': 'Ready to collect data'
}

def run_data_collection():
    """Run data collection in background thread"""
    global data_collection_status

    try:
        data_collection_status['running'] = True
        data_collection_status['message'] = 'Collecting economic data...'

        # Import and run the main data collection process
        from src.main import main as run_main_process

        logger.info("Starting background data collection")
        success = run_main_process()

        data_collection_status['running'] = False
        data_collection_status['last_run'] = datetime.now().isoformat()

        if success:
            data_collection_status['last_success'] = datetime.now().isoformat()
            data_collection_status['message'] = 'Data collection completed successfully'
            logger.info("Background data collection completed successfully")
        else:
            data_collection_status['message'] = 'Data collection failed - check logs for details'
            logger.error("Background data collection failed")

    except Exception as e:
        data_collection_status['running'] = False
        data_collection_status['message'] = f'Data collection error: {str(e)}'
        logger.error(f"Error in background data collection: {str(e)}")

@app.route('/')
def index():
    """Main dashboard page"""
    try:
        # Check if dashboard data exists
        dashboard_data_path = 'data/dashboard_data.json'
        dashboard_data = None

        if os.path.exists(dashboard_data_path):
            with open(dashboard_data_path, 'r') as f:
                dashboard_data = json.load(f)

        # Get data collection status
        status = data_collection_status.copy()

        return render_template('dashboard.html',
                             dashboard_data=dashboard_data,
                             status=status)
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        return render_template('dashboard.html',
                             dashboard_data=None,
                             status=data_collection_status,
                             error=str(e))

@app.route('/collect-data', methods=['POST'])
def trigger_data_collection():
    """Trigger data collection process"""
    global data_collection_status

    if data_collection_status['running']:
        return jsonify({
            'success': False,
            'message': 'Data collection is already running'
        }), 400

    try:
        # Start data collection in background thread
        thread = threading.Thread(target=run_data_collection)
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': 'Data collection started in background'
        })
    except Exception as e:
        logger.error(f"Error starting data collection: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Failed to start data collection: {str(e)}'
        }), 500

@app.route('/status')
def get_status():
    """Get current system status"""
    return jsonify(data_collection_status)

@app.route('/dashboard-data')
def get_dashboard_data():
    """Get dashboard data as JSON"""
    try:
        dashboard_data_path = 'data/dashboard_data.json'
        if os.path.exists(dashboard_data_path):
            with open(dashboard_data_path, 'r') as f:
                data = json.load(f)
            return jsonify(data)
        else:
            return jsonify({
                'error': 'No dashboard data available',
                'message': 'Please run data collection first'
            }), 404
    except Exception as e:
        logger.error(f"Error loading dashboard data: {str(e)}")
        return jsonify({
            'error': 'Failed to load dashboard data',
            'message': str(e)
        }), 500

@app.route('/export/<pair>')
def export_pair_data(pair):
    """Export data for a specific currency pair"""
    try:
        dashboard_data_path = 'data/dashboard_data.json'
        if not os.path.exists(dashboard_data_path):
            return jsonify({
                'error': 'No data available for export'
            }), 404

        with open(dashboard_data_path, 'r') as f:
            dashboard_data = json.load(f)

        if pair not in dashboard_data.get('currency_pairs', {}):
            return jsonify({
                'error': f'No data available for pair {pair}'
            }), 404

        pair_data = dashboard_data['currency_pairs'][pair]

        # Create CSV content
        csv_content = "Indicator,Impact\n"
        for indicator in pair_data.get('top_indicators', []):
            csv_content += f"{indicator['name']},{indicator['impact']}\n"

        # Create temporary file
        import tempfile
        import io

        output = io.StringIO()
        output.write(csv_content)
        output.seek(0)

        return send_file(
            io.BytesIO(output.getvalue().encode('utf-8')),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f"{pair.replace('/', '')}_indicators.csv"
        )

    except Exception as e:
        logger.error(f"Error exporting data for {pair}: {str(e)}")
        return jsonify({
            'error': 'Export failed',
            'message': str(e)
        }), 500

@app.route('/logs')
def view_logs():
    """View application logs"""
    try:
        logs = []
        log_files = ['app.log', 'main.log', 'tradingeconomics_scraper.log', 'dashboard_updater.log']

        for log_file in log_files:
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    # Get last 50 lines
                    recent_lines = lines[-50:] if len(lines) > 50 else lines
                    logs.append({
                        'file': log_file,
                        'lines': recent_lines
                    })

        return render_template('logs.html', logs=logs)
    except Exception as e:
        logger.error(f"Error loading logs: {str(e)}")
        return f"Error loading logs: {str(e)}", 500

@app.route('/health')
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        with app.app_context():
            db.session.execute('SELECT 1')

        # Check if data directory exists
        data_exists = os.path.exists('data')

        # Check last data collection
        dashboard_data_path = 'data/dashboard_data.json'
        data_fresh = False
        if os.path.exists(dashboard_data_path):
            stat = os.stat(dashboard_data_path)
            age_hours = (time.time() - stat.st_mtime) / 3600
            data_fresh = age_hours < 24  # Data is fresh if less than 24 hours old

        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'data_directory': 'exists' if data_exists else 'missing',
            'data_freshness': 'fresh' if data_fresh else 'stale',
            'collection_status': data_collection_status['message'],
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return render_template('error.html',
                         error_code=404,
                         error_message="Page not found"), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return render_template('error.html',
                         error_code=500,
                         error_message="Internal server error"), 500

if __name__ == '__main__':
    logger.info("Starting Forex Analysis Web Application")

    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
