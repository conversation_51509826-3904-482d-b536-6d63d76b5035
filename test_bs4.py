#!/usr/bin/env python3
"""
Test BeautifulSoup import specifically
"""

print("Testing BeautifulSoup import...")

try:
    from bs4 import BeautifulSoup
    print("✅ SUCCESS: BeautifulSoup imported correctly!")
    print(f"   Version: {BeautifulSoup.__doc__}")
    
    # Test basic functionality
    html = "<html><body><p>Test</p></body></html>"
    soup = BeautifulSoup(html, 'html.parser')
    print(f"   Test parse: {soup.find('p').text}")
    
except ImportError as e:
    print(f"❌ FAILED: {e}")
    print("\nTrying to install beautifulsoup4...")
    import subprocess
    import sys
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "beautifulsoup4"])
        print("✅ Installation completed, testing again...")
        
        from bs4 import BeautifulSoup
        print("✅ SUCCESS: BeautifulSoup now works!")
        
    except Exception as install_error:
        print(f"❌ Installation failed: {install_error}")

print("\nPress Enter to continue...")
input()
