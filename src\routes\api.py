"""
API routes for forex data collection and dashboard
"""

from flask import Blueprint, jsonify, request, send_file
import os
import json
import pandas as pd
from datetime import datetime
import io
import csv
from src.models.economic_data import db, EconomicIndicator, DashboardData
from src.tradingeconomics_scraper import run_scraper, CURRENCY_PAIRS

# Create blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

# Data directory - use relative paths
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_DIR = os.path.join(BASE_DIR, 'data')
ECONOMIC_DATA_DIR = os.path.join(DATA_DIR, 'economic_indicators')

# Ensure data directories exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(ECONOMIC_DATA_DIR, exist_ok=True)

@api_bp.route('/collect-data', methods=['POST'])
def collect_data():
    """Trigger data collection process"""
    try:
        # Run the data collection
        success = run_scraper()

        if success:
            # Load the dashboard data
            dashboard_data_path = f"{DATA_DIR}/dashboard_data.json"
            if os.path.exists(dashboard_data_path):
                with open(dashboard_data_path, 'r') as f:
                    dashboard_data = json.load(f)

                # Store dashboard data in database for history
                timestamp = datetime.now()
                for pair, data in dashboard_data['currency_pairs'].items():
                    db_data = DashboardData(
                        pair=pair,
                        score=data['score'],
                        signal=data['signal'],
                        confidence=data['confidence'],
                        optimal_horizon=data['optimal_horizon'],
                        timestamp=timestamp
                    )
                    db.session.add(db_data)

                # Store economic indicators in database for history
                for indicator_file in os.listdir(ECONOMIC_DATA_DIR):
                    if indicator_file.endswith('.csv'):
                        indicator_type = os.path.splitext(indicator_file)[0]
                        file_path = os.path.join(ECONOMIC_DATA_DIR, indicator_file)

                        try:
                            df = pd.read_csv(file_path)

                            # Handle different CSV formats
                            if 'currency' in df.columns:
                                for _, row in df.iterrows():
                                    if 'value' in df.columns:
                                        value_col = 'value'
                                    elif indicator_type in df.columns:
                                        value_col = indicator_type
                                    else:
                                        # Try to find a numeric column
                                        for col in df.columns:
                                            if col not in ['currency', 'country', 'date', 'last_update']:
                                                value_col = col
                                                break

                                    if 'currency' in row and value_col in row:
                                        indicator = EconomicIndicator(
                                            indicator_type=indicator_type,
                                            currency=row['currency'],
                                            value=float(row[value_col]),
                                            timestamp=timestamp
                                        )
                                        db.session.add(indicator)
                        except Exception as e:
                            print(f"Error processing {indicator_file}: {str(e)}")

                db.session.commit()

                return jsonify({
                    'success': True,
                    'message': 'Data collection completed successfully',
                    'timestamp': timestamp.isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Data collection completed but dashboard data not found'
                }), 500
        else:
            return jsonify({
                'success': False,
                'message': 'Data collection failed'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error during data collection: {str(e)}'
        }), 500

@api_bp.route('/dashboard-data', methods=['GET'])
def get_dashboard_data():
    """Get the latest dashboard data"""
    try:
        dashboard_data_path = f"{DATA_DIR}/dashboard_data.json"
        if os.path.exists(dashboard_data_path):
            with open(dashboard_data_path, 'r') as f:
                dashboard_data = json.load(f)

            # Add history data for each pair
            for pair in CURRENCY_PAIRS:
                history = DashboardData.get_latest_for_pair(pair, limit=3)
                dashboard_data['currency_pairs'][pair]['history'] = [
                    {
                        'score': h.score,
                        'signal': h.signal,
                        'confidence': h.confidence,
                        'timestamp': h.timestamp.isoformat()
                    } for h in history
                ]

            return jsonify(dashboard_data)
        else:
            return jsonify({
                'success': False,
                'message': 'Dashboard data not found'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving dashboard data: {str(e)}'
        }), 500

@api_bp.route('/indicator-history/<indicator_type>/<currency>', methods=['GET'])
def get_indicator_history(indicator_type, currency):
    """Get history for a specific indicator and currency"""
    try:
        history = EconomicIndicator.get_latest_for_currency(indicator_type, currency, limit=3)
        return jsonify({
            'success': True,
            'history': [
                {
                    'value': h.value,
                    'timestamp': h.timestamp.isoformat()
                } for h in history
            ]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error retrieving indicator history: {str(e)}'
        }), 500

@api_bp.route('/export-csv/<indicator_type>', methods=['GET'])
def export_csv(indicator_type):
    """Export indicator data as CSV"""
    try:
        # Check if the indicator file exists
        file_path = os.path.join(ECONOMIC_DATA_DIR, f"{indicator_type}.csv")
        if os.path.exists(file_path):
            return send_file(
                file_path,
                mimetype='text/csv',
                as_attachment=True,
                download_name=f"{indicator_type}_{datetime.now().strftime('%Y%m%d')}.csv"
            )

        # If file doesn't exist, generate from database
        indicators = EconomicIndicator.query.filter_by(indicator_type=indicator_type).all()
        if not indicators:
            return jsonify({
                'success': False,
                'message': f'No data found for indicator: {indicator_type}'
            }), 404

        # Create CSV in memory
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['currency', 'value', 'timestamp'])

        # Write data
        for indicator in indicators:
            writer.writerow([
                indicator.currency,
                indicator.value,
                indicator.timestamp.isoformat()
            ])

        # Create response
        output.seek(0)
        return send_file(
            io.BytesIO(output.getvalue().encode('utf-8')),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f"{indicator_type}_{datetime.now().strftime('%Y%m%d')}.csv"
        )
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error exporting CSV: {str(e)}'
        }), 500

@api_bp.route('/export-all-csv', methods=['GET'])
def export_all_csv():
    """Export all indicator data as ZIP of CSVs"""
    try:
        # Create a ZIP file in memory
        import zipfile
        memory_file = io.BytesIO()

        with zipfile.ZipFile(memory_file, 'w') as zf:
            # Add all CSV files from the economic data directory
            for indicator_file in os.listdir(ECONOMIC_DATA_DIR):
                if indicator_file.endswith('.csv'):
                    file_path = os.path.join(ECONOMIC_DATA_DIR, indicator_file)
                    zf.write(file_path, arcname=indicator_file)

        # Seek to the beginning of the file
        memory_file.seek(0)

        # Return the ZIP file
        return send_file(
            memory_file,
            mimetype='application/zip',
            as_attachment=True,
            download_name=f"forex_economic_data_{datetime.now().strftime('%Y%m%d')}.zip"
        )
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error exporting all CSV data: {str(e)}'
        }), 500
