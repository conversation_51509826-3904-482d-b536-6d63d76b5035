2025-05-26 20:33:01,500 - __main__ - INFO - Starting Forex Price Action Analysis System
2025-05-26 20:33:01,500 - __main__ - INFO - Running Trading Economics scraper...
2025-05-26 20:33:02,141 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-26 20:33:02,141 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-26 20:33:02,141 - tradingeconomics_scraper - INFO - Using cached data for new-zealand
2025-05-26 20:33:02,142 - tradingeconomics_scraper - INFO - Loaded new-zealand indicators from cache
2025-05-26 20:33:05,150 - tradingeconomics_scraper - INFO - Using cached data for japan
2025-05-26 20:33:05,151 - tradingeconomics_scraper - INFO - Loaded japan indicators from cache
2025-05-26 20:33:08,551 - tradingeconomics_scraper - INFO - Using cached data for australia
2025-05-26 20:33:08,551 - tradingeconomics_scraper - INFO - Loaded australia indicators from cache
2025-05-26 20:33:12,656 - tradingeconomics_scraper - INFO - Using cached data for switzerland
2025-05-26 20:33:12,656 - tradingeconomics_scraper - INFO - Loaded switzerland indicators from cache
2025-05-26 20:33:17,575 - tradingeconomics_scraper - INFO - Using cached data for canada
2025-05-26 20:33:17,576 - tradingeconomics_scraper - INFO - Loaded canada indicators from cache
2025-05-26 20:33:20,433 - tradingeconomics_scraper - INFO - Using cached data for euro-area
2025-05-26 20:33:20,434 - tradingeconomics_scraper - INFO - Loaded euro-area indicators from cache
2025-05-26 20:33:25,174 - tradingeconomics_scraper - INFO - Using cached data for united-kingdom
2025-05-26 20:33:25,175 - tradingeconomics_scraper - INFO - Loaded united-kingdom indicators from cache
2025-05-26 20:33:29,001 - tradingeconomics_scraper - INFO - Using cached data for united-states
2025-05-26 20:33:29,001 - tradingeconomics_scraper - INFO - Loaded united-states indicators from cache
2025-05-26 20:33:33,104 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-26 20:33:33,104 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-26 20:33:33,104 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-26 20:33:33,104 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-26 20:33:33,104 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-26 20:33:33,104 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-26 20:33:33,104 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-26 20:33:33,105 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-26 20:33:33,106 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-26 20:33:33,106 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-26 20:33:33,108 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 20:33:33,108 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-26 20:33:33,108 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-26 20:33:33,108 - __main__ - INFO - Updating dashboard...
2025-05-26 20:33:33,111 - dashboard_updater - INFO - Starting dashboard updater for Forex Price Action Analysis System
2025-05-26 20:33:33,111 - dashboard_updater - INFO - Updating dashboard HTML for local
2025-05-26 20:33:33,111 - dashboard_updater - INFO - Loaded dashboard data from /home/<USER>/forex_web_app_local/data/dashboard_data.json
2025-05-26 20:33:33,112 - dashboard_updater - INFO - Dashboard HTML updated successfully: /home/<USER>/forex_web_app_local/data/dashboard.html
2025-05-26 20:33:33,112 - dashboard_updater - INFO - Dashboard updater completed successfully
2025-05-26 20:33:33,112 - __main__ - INFO - Dashboard updated successfully
2025-05-26 20:33:33,112 - __main__ - INFO - Copying dashboard HTML to root directory...
2025-05-26 20:33:33,112 - __main__ - INFO - Dashboard HTML copied to /home/<USER>/forex_web_app_local/src/../dashboard.html
2025-05-26 20:33:33,112 - __main__ - INFO - Exporting data to CSV files...
2025-05-26 20:33:33,113 - __main__ - ERROR - Error exporting data: [Errno 2] No such file or directory: '/home/<USER>/forex_web_app_local/src/../data/exports/EUR/USD_indicators.csv'
2025-05-26 20:33:33,113 - __main__ - ERROR - Failed to export data to CSV files
2025-05-26 20:33:33,113 - __main__ - INFO - Forex Price Action Analysis System completed successfully
2025-05-26 19:43:13,362 - __main__ - INFO - Starting Forex Price Action Analysis System
2025-05-26 19:43:13,363 - __main__ - INFO - Running Trading Economics scraper...
2025-05-26 19:43:14,099 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-26 19:43:14,100 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-26 19:43:14,100 - tradingeconomics_scraper - INFO - Scraping indicators for new-zealand
2025-05-26 19:43:15,777 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for new-zealand
2025-05-26 19:43:15,779 - tradingeconomics_scraper - INFO - Saved new-zealand indicators to cache
2025-05-26 19:43:19,866 - tradingeconomics_scraper - INFO - Scraping indicators for japan
2025-05-26 19:43:22,700 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for japan
2025-05-26 19:43:22,702 - tradingeconomics_scraper - INFO - Saved japan indicators to cache
2025-05-26 19:43:27,061 - tradingeconomics_scraper - INFO - Scraping indicators for canada
2025-05-26 19:43:30,431 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for canada
2025-05-26 19:43:30,441 - tradingeconomics_scraper - INFO - Saved canada indicators to cache
2025-05-26 19:43:34,861 - tradingeconomics_scraper - INFO - Scraping indicators for switzerland
2025-05-26 19:43:36,349 - tradingeconomics_scraper - INFO - Successfully scraped 19 indicators for switzerland
2025-05-26 19:43:36,351 - tradingeconomics_scraper - INFO - Saved switzerland indicators to cache
2025-05-26 19:43:40,271 - tradingeconomics_scraper - INFO - Scraping indicators for united-kingdom
2025-05-26 19:43:43,006 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for united-kingdom
2025-05-26 19:43:43,007 - tradingeconomics_scraper - INFO - Saved united-kingdom indicators to cache
2025-05-26 19:43:47,736 - tradingeconomics_scraper - INFO - Scraping indicators for euro-area
2025-05-26 19:43:49,788 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for euro-area
2025-05-26 19:43:49,791 - tradingeconomics_scraper - INFO - Saved euro-area indicators to cache
2025-05-26 19:43:54,077 - tradingeconomics_scraper - INFO - Scraping indicators for united-states
2025-05-26 19:43:56,418 - tradingeconomics_scraper - INFO - Successfully scraped 23 indicators for united-states
2025-05-26 19:43:56,420 - tradingeconomics_scraper - INFO - Saved united-states indicators to cache
2025-05-26 19:43:59,437 - tradingeconomics_scraper - INFO - Scraping indicators for australia
2025-05-26 19:44:02,600 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for australia
2025-05-26 19:44:02,602 - tradingeconomics_scraper - INFO - Saved australia indicators to cache
2025-05-26 19:44:05,294 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-26 19:44:05,294 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-26 19:44:05,295 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-26 19:44:05,295 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-26 19:44:05,295 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-26 19:44:05,295 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-26 19:44:05,296 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-26 19:44:05,296 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-26 19:44:05,297 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-26 19:44:05,297 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-26 19:44:05,297 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-26 19:44:05,298 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-26 19:44:05,298 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-26 19:44:05,298 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-26 19:44:05,298 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-26 19:44:05,300 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-26 19:44:05,300 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-26 19:44:05,308 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-26 19:44:05,308 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-26 19:44:05,308 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-26 19:44:05,309 - __main__ - INFO - Updating dashboard...
2025-05-26 19:44:05,316 - dashboard_updater - INFO - Starting dashboard updater for Forex Price Action Analysis System
2025-05-26 19:44:05,316 - dashboard_updater - INFO - Updating dashboard HTML for local
2025-05-26 19:44:05,317 - dashboard_updater - INFO - Loaded dashboard data from /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-26 19:44:05,318 - dashboard_updater - ERROR - Error updating dashboard HTML: [Errno 2] No such file or directory: '/home/<USER>/forex_web_app_local\\src\\templates\\index.html'
2025-05-26 19:44:05,318 - dashboard_updater - ERROR - Failed to update dashboard
2025-05-26 19:44:05,318 - __main__ - ERROR - Dashboard update failed
2025-05-26 19:44:05,318 - __main__ - ERROR - Failed to update dashboard
2025-05-26 19:47:20,272 - __main__ - INFO - Starting Forex Price Action Analysis System
2025-05-26 19:47:20,272 - __main__ - INFO - Running Trading Economics scraper...
2025-05-26 19:47:20,765 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-26 19:47:20,766 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-26 19:47:20,766 - tradingeconomics_scraper - INFO - Using cached data for australia
2025-05-26 19:47:20,766 - tradingeconomics_scraper - INFO - Loaded australia indicators from cache
2025-05-26 19:47:24,317 - tradingeconomics_scraper - INFO - Using cached data for euro-area
2025-05-26 19:47:24,318 - tradingeconomics_scraper - INFO - Loaded euro-area indicators from cache
2025-05-26 19:47:26,337 - tradingeconomics_scraper - INFO - Using cached data for united-kingdom
2025-05-26 19:47:26,338 - tradingeconomics_scraper - INFO - Loaded united-kingdom indicators from cache
2025-05-26 19:47:29,735 - tradingeconomics_scraper - INFO - Using cached data for canada
2025-05-26 19:47:29,736 - tradingeconomics_scraper - INFO - Loaded canada indicators from cache
2025-05-26 19:47:33,099 - tradingeconomics_scraper - INFO - Using cached data for switzerland
2025-05-26 19:47:33,101 - tradingeconomics_scraper - INFO - Loaded switzerland indicators from cache
2025-05-26 19:47:37,048 - tradingeconomics_scraper - INFO - Using cached data for united-states
2025-05-26 19:47:37,050 - tradingeconomics_scraper - INFO - Loaded united-states indicators from cache
2025-05-26 19:47:39,665 - tradingeconomics_scraper - INFO - Using cached data for japan
2025-05-26 19:47:39,666 - tradingeconomics_scraper - INFO - Loaded japan indicators from cache
2025-05-26 19:47:43,441 - tradingeconomics_scraper - INFO - Using cached data for new-zealand
2025-05-26 19:47:43,442 - tradingeconomics_scraper - INFO - Loaded new-zealand indicators from cache
2025-05-26 19:47:46,260 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-26 19:47:46,260 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-26 19:47:46,261 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-26 19:47:46,261 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-26 19:47:46,262 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-26 19:47:46,262 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-26 19:47:46,263 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-26 19:47:46,263 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-26 19:47:46,264 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-26 19:47:46,264 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-26 19:47:46,264 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-26 19:47:46,265 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-26 19:47:46,265 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-26 19:47:46,266 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-26 19:47:46,266 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-26 19:47:46,269 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-26 19:47:46,269 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-26 19:47:46,277 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-26 19:47:46,278 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-26 19:47:46,278 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-26 19:47:46,279 - __main__ - INFO - Updating dashboard...
2025-05-26 19:47:46,282 - dashboard_updater - INFO - Starting dashboard updater for Forex Price Action Analysis System
2025-05-26 19:47:46,283 - dashboard_updater - INFO - Updating dashboard HTML for local
2025-05-26 19:47:46,284 - dashboard_updater - INFO - Loaded dashboard data from /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-26 19:47:46,284 - dashboard_updater - ERROR - Error updating dashboard HTML: [Errno 2] No such file or directory: '/home/<USER>/forex_web_app_local\\src\\templates\\index.html'
2025-05-26 19:47:46,285 - dashboard_updater - ERROR - Failed to update dashboard
2025-05-26 19:47:46,285 - __main__ - ERROR - Dashboard update failed
2025-05-26 19:47:46,286 - __main__ - ERROR - Failed to update dashboard
2025-05-27 17:35:23,433 - __main__ - INFO - Starting Forex Price Action Analysis System
2025-05-27 17:35:23,434 - __main__ - INFO - Running Trading Economics scraper...
2025-05-27 17:35:24,269 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-27 17:35:24,269 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-27 17:35:24,271 - tradingeconomics_scraper - INFO - Scraping indicators for canada
2025-05-27 17:35:25,771 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for canada
2025-05-27 17:35:25,772 - tradingeconomics_scraper - INFO - Saved canada indicators to cache
2025-05-27 17:35:28,028 - tradingeconomics_scraper - INFO - Scraping indicators for euro-area
2025-05-27 17:35:31,169 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for euro-area
2025-05-27 17:35:31,172 - tradingeconomics_scraper - INFO - Saved euro-area indicators to cache
2025-05-27 17:35:33,958 - tradingeconomics_scraper - INFO - Scraping indicators for switzerland
2025-05-27 17:35:36,516 - tradingeconomics_scraper - INFO - Successfully scraped 19 indicators for switzerland
2025-05-27 17:35:36,518 - tradingeconomics_scraper - INFO - Saved switzerland indicators to cache
2025-05-27 17:35:39,930 - tradingeconomics_scraper - INFO - Scraping indicators for united-states
2025-05-27 17:35:41,981 - tradingeconomics_scraper - INFO - Successfully scraped 23 indicators for united-states
2025-05-27 17:35:41,983 - tradingeconomics_scraper - INFO - Saved united-states indicators to cache
2025-05-27 17:35:46,722 - tradingeconomics_scraper - INFO - Scraping indicators for new-zealand
2025-05-27 17:35:50,054 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for new-zealand
2025-05-27 17:35:50,056 - tradingeconomics_scraper - INFO - Saved new-zealand indicators to cache
2025-05-27 17:35:52,609 - tradingeconomics_scraper - INFO - Scraping indicators for united-kingdom
2025-05-27 17:35:54,227 - tradingeconomics_scraper - INFO - Successfully scraped 20 indicators for united-kingdom
2025-05-27 17:35:54,229 - tradingeconomics_scraper - INFO - Saved united-kingdom indicators to cache
2025-05-27 17:35:58,842 - tradingeconomics_scraper - INFO - Scraping indicators for australia
2025-05-27 17:36:00,623 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for australia
2025-05-27 17:36:00,625 - tradingeconomics_scraper - INFO - Saved australia indicators to cache
2025-05-27 17:36:03,713 - tradingeconomics_scraper - INFO - Scraping indicators for japan
2025-05-27 17:36:06,003 - tradingeconomics_scraper - INFO - Successfully scraped 21 indicators for japan
2025-05-27 17:36:06,005 - tradingeconomics_scraper - INFO - Saved japan indicators to cache
2025-05-27 17:36:10,374 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-27 17:36:10,375 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-27 17:36:10,375 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-27 17:36:10,376 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-27 17:36:10,377 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-27 17:36:10,377 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-27 17:36:10,378 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-27 17:36:10,378 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-27 17:36:10,378 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-27 17:36:10,379 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-27 17:36:10,379 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-27 17:36:10,380 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-27 17:36:10,380 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-27 17:36:10,380 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-27 17:36:10,380 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-27 17:36:10,392 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-27 17:36:10,393 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-27 17:36:10,397 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-27 17:36:10,398 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-27 17:36:10,398 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-27 17:36:10,398 - __main__ - INFO - Updating dashboard...
2025-05-27 17:36:10,400 - dashboard_updater - INFO - Starting dashboard updater for Forex Price Action Analysis System
2025-05-27 17:36:10,400 - dashboard_updater - INFO - Updating dashboard HTML for local
2025-05-27 17:36:10,401 - dashboard_updater - INFO - Loaded dashboard data from /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-27 17:36:10,402 - dashboard_updater - ERROR - Error updating dashboard HTML: [Errno 2] No such file or directory: '/home/<USER>/forex_web_app_local\\src\\templates\\index.html'
2025-05-27 17:36:10,402 - dashboard_updater - ERROR - Failed to update dashboard
2025-05-27 17:36:10,403 - __main__ - ERROR - Dashboard update failed
2025-05-27 17:36:10,403 - __main__ - ERROR - Failed to update dashboard
2025-05-27 17:40:45,105 - __main__ - INFO - Starting Forex Price Action Analysis System
2025-05-27 17:40:45,105 - __main__ - INFO - Running Trading Economics scraper...
2025-05-27 17:40:45,608 - tradingeconomics_scraper - INFO - Starting Trading Economics scraper for Forex Price Action Analysis System
2025-05-27 17:40:45,608 - tradingeconomics_scraper - INFO - Starting to scrape all countries
2025-05-27 17:40:45,609 - tradingeconomics_scraper - INFO - Using cached data for canada
2025-05-27 17:40:45,611 - tradingeconomics_scraper - INFO - Loaded canada indicators from cache
2025-05-27 17:40:48,993 - tradingeconomics_scraper - INFO - Using cached data for australia
2025-05-27 17:40:48,994 - tradingeconomics_scraper - INFO - Loaded australia indicators from cache
2025-05-27 17:40:53,644 - tradingeconomics_scraper - INFO - Using cached data for switzerland
2025-05-27 17:40:53,645 - tradingeconomics_scraper - INFO - Loaded switzerland indicators from cache
2025-05-27 17:40:57,529 - tradingeconomics_scraper - INFO - Using cached data for united-states
2025-05-27 17:40:57,531 - tradingeconomics_scraper - INFO - Loaded united-states indicators from cache
2025-05-27 17:41:00,824 - tradingeconomics_scraper - INFO - Using cached data for new-zealand
2025-05-27 17:41:00,825 - tradingeconomics_scraper - INFO - Loaded new-zealand indicators from cache
2025-05-27 17:41:05,308 - tradingeconomics_scraper - INFO - Using cached data for japan
2025-05-27 17:41:05,309 - tradingeconomics_scraper - INFO - Loaded japan indicators from cache
2025-05-27 17:41:07,865 - tradingeconomics_scraper - INFO - Using cached data for euro-area
2025-05-27 17:41:07,867 - tradingeconomics_scraper - INFO - Loaded euro-area indicators from cache
2025-05-27 17:41:11,681 - tradingeconomics_scraper - INFO - Using cached data for united-kingdom
2025-05-27 17:41:11,681 - tradingeconomics_scraper - INFO - Loaded united-kingdom indicators from cache
2025-05-27 17:41:15,245 - tradingeconomics_scraper - INFO - Completed scraping 8 countries
2025-05-27 17:41:15,246 - tradingeconomics_scraper - INFO - Generating analysis for EUR/USD
2025-05-27 17:41:15,246 - tradingeconomics_scraper - INFO - Analysis for EUR/USD: Score=-0.8900000000000001, Signal=Sell, Confidence=80.0%
2025-05-27 17:41:15,247 - tradingeconomics_scraper - INFO - Generating analysis for USD/JPY
2025-05-27 17:41:15,247 - tradingeconomics_scraper - INFO - Analysis for USD/JPY: Score=1.8000000000000003, Signal=Strong Buy, Confidence=80.0%
2025-05-27 17:41:15,248 - tradingeconomics_scraper - INFO - Generating analysis for GBP/USD
2025-05-27 17:41:15,248 - tradingeconomics_scraper - INFO - Analysis for GBP/USD: Score=-0.29000000000000004, Signal=Neutral, Confidence=80.0%
2025-05-27 17:41:15,249 - tradingeconomics_scraper - INFO - Generating analysis for USD/CHF
2025-05-27 17:41:15,249 - tradingeconomics_scraper - INFO - Analysis for USD/CHF: Score=0.6700000000000003, Signal=Buy, Confidence=80.0%
2025-05-27 17:41:15,250 - tradingeconomics_scraper - INFO - Generating analysis for USD/CAD
2025-05-27 17:41:15,251 - tradingeconomics_scraper - INFO - Analysis for USD/CAD: Score=0.6100000000000001, Signal=Buy, Confidence=80.0%
2025-05-27 17:41:15,251 - tradingeconomics_scraper - INFO - Generating analysis for AUD/USD
2025-05-27 17:41:15,251 - tradingeconomics_scraper - INFO - Analysis for AUD/USD: Score=-0.09999999999999994, Signal=Neutral, Confidence=80.0%
2025-05-27 17:41:15,251 - tradingeconomics_scraper - INFO - Generating analysis for NZD/USD
2025-05-27 17:41:15,252 - tradingeconomics_scraper - INFO - Analysis for NZD/USD: Score=-0.35000000000000003, Signal=Sell, Confidence=80.0%
2025-05-27 17:41:15,254 - tradingeconomics_scraper - INFO - Updated history with new data points
2025-05-27 17:41:15,254 - tradingeconomics_scraper - INFO - Creating consolidated dashboard data...
2025-05-27 17:41:15,263 - tradingeconomics_scraper - INFO - Saved consolidated dashboard data to /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-27 17:41:15,263 - tradingeconomics_scraper - INFO - Trading Economics scraper completed successfully
2025-05-27 17:41:15,263 - __main__ - INFO - Trading Economics scraper completed successfully
2025-05-27 17:41:15,263 - __main__ - INFO - Updating dashboard...
2025-05-27 17:41:15,265 - dashboard_updater - INFO - Starting dashboard updater for Forex Price Action Analysis System
2025-05-27 17:41:15,265 - dashboard_updater - INFO - Updating dashboard HTML for local
2025-05-27 17:41:15,265 - dashboard_updater - INFO - Loaded dashboard data from /home/<USER>/forex_web_app_local\data\dashboard_data.json
2025-05-27 17:41:15,266 - dashboard_updater - ERROR - Error updating dashboard HTML: [Errno 2] No such file or directory: '/home/<USER>/forex_web_app_local\\src\\templates\\index.html'
2025-05-27 17:41:15,266 - dashboard_updater - ERROR - Failed to update dashboard
2025-05-27 17:41:15,267 - __main__ - ERROR - Dashboard update failed
2025-05-27 17:41:15,267 - __main__ - ERROR - Failed to update dashboard
