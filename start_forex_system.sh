#!/bin/bash

# Forex Analysis System Startup Script for Mac/Linux
# Make this file executable with: chmod +x start_forex_system.sh

echo "========================================"
echo "   FOREX ANALYSIS SYSTEM STARTUP"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 is not installed"
    echo "Please install Python 3.8 or higher"
    echo "Visit: https://www.python.org/downloads/"
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ Python found:"
python3 --version

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ ERROR: pip3 is not available"
    echo "Please ensure pip is installed with Python"
    read -p "Press Enter to exit..."
    exit 1
fi

echo
echo "📦 Installing/Updating dependencies..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo
    echo "❌ ERROR: Failed to install dependencies"
    echo "Please check your internet connection and try again"
    read -p "Press Enter to exit..."
    exit 1
fi

echo
echo "========================================"
echo "   STARTING FOREX ANALYSIS SYSTEM"
echo "========================================"
echo
echo "🚀 The system will start in a few seconds..."
echo "🌐 Your browser will open automatically"
echo
echo "To stop the system, press Ctrl+C"
echo

# Start the application
python3 run.py

echo
echo "🛑 System has stopped."
read -p "Press Enter to exit..."
