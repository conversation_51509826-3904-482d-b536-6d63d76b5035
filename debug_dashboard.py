#!/usr/bin/env python3
"""
Debug script to check dashboard data loading
"""

import os
import json

def check_dashboard_data():
    """Check if dashboard data exists and is valid"""
    
    dashboard_data_path = 'data/dashboard_data.json'
    
    print("🔍 DASHBOARD DATA DEBUG")
    print("=" * 30)
    
    # Check if file exists
    if os.path.exists(dashboard_data_path):
        print(f"✅ File exists: {dashboard_data_path}")
        print(f"   File size: {os.path.getsize(dashboard_data_path)} bytes")
        
        try:
            with open(dashboard_data_path, 'r') as f:
                data = json.load(f)
            
            print(f"✅ JSON is valid")
            print(f"   Timestamp: {data.get('timestamp', 'Not found')}")
            
            currency_pairs = data.get('currency_pairs', {})
            print(f"   Currency pairs: {len(currency_pairs)}")
            
            for pair, pair_data in currency_pairs.items():
                print(f"   - {pair}: {pair_data.get('signal', 'No signal')} (Score: {pair_data.get('score', 'No score')})")
            
            return data
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            return None
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return None
    else:
        print(f"❌ File not found: {dashboard_data_path}")
        print(f"   Current directory: {os.getcwd()}")
        print(f"   Data directory exists: {os.path.exists('data')}")
        
        # List files in data directory
        if os.path.exists('data'):
            files = os.listdir('data')
            print(f"   Files in data directory: {files}")
        
        return None

def test_template_rendering():
    """Test template rendering with sample data"""
    
    print("\n🎨 TEMPLATE RENDERING TEST")
    print("=" * 30)
    
    # Sample data
    sample_data = {
        "timestamp": "2025-01-11 12:00:00",
        "currency_pairs": {
            "EUR/USD": {
                "score": -0.89,
                "signal": "Sell",
                "confidence": 80,
                "optimal_horizon": 7,
                "top_indicators": [
                    {"name": "Interest Rate", "impact": -0.9},
                    {"name": "Unemployment Rate", "impact": 0.7}
                ]
            }
        }
    }
    
    # Test Jinja2 template logic
    try:
        from jinja2 import Template
        
        template_str = """
        {% if dashboard_data and dashboard_data.currency_pairs %}
            <p>Data found: {{ dashboard_data.currency_pairs|length }} pairs</p>
            {% for pair, data in dashboard_data.currency_pairs.items() %}
                <p>{{ pair }}: {{ data.signal }}</p>
            {% endfor %}
        {% else %}
            <p>No data available</p>
        {% endif %}
        """
        
        template = Template(template_str)
        result = template.render(dashboard_data=sample_data)
        
        print("✅ Template rendering works:")
        print(result)
        
    except Exception as e:
        print(f"❌ Template rendering error: {e}")

def main():
    """Main debug function"""
    
    # Check dashboard data
    data = check_dashboard_data()
    
    # Test template rendering
    test_template_rendering()
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    print("=" * 30)
    
    if data:
        print("✅ Data is available - the issue might be in template rendering")
        print("   Try refreshing the browser page")
        print("   Check browser console for JavaScript errors")
    else:
        print("❌ No data available - run data collection first")
        print("   Click 'Collect Data' button in the web interface")
        print("   Or run: python src/main.py")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
