# Forex System Final V2 - Setup Guide

## Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 2GB RAM
- **Storage**: 1GB free space for data and logs
- **Network**: Internet connection for data scraping

### Required Software
- Python 3.8+
- pip (Python package manager)
- Git (for version control)
- Web browser (for dashboard viewing)

## Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd forex_system_final_v2/local
```

### 2. Create Virtual Environment (Recommended)
```bash
# Windows
python -m venv forex_env
forex_env\Scripts\activate

# macOS/Linux
python3 -m venv forex_env
source forex_env/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Verify Installation
```bash
python -c "import flask, requests, bs4, pandas; print('All dependencies installed successfully')"
```

## Configuration

### 1. Directory Structure Setup
The system will automatically create necessary directories:
```
data/
├── cache/              # Cached economic data
├── economic_indicators/ # CSV exports
├── exports/            # Data exports
└── dashboard.html      # Generated dashboard

instance/
└── forex_data.db      # SQLite database

logs/
├── main.log
├── tradingeconomics_scraper.log
└── dashboard_updater.log
```

### 2. Database Setup
The system uses SQLite by default (no additional setup required).

For MySQL (optional):
```python
# Update database configuration in models
DATABASE_URL = 'mysql+pymysql://user:password@localhost/forex_db'
```

### 3. Environment Variables (Optional)
```bash
# Set application type
export FOREX_APP_TYPE=local

# Set custom data directory
export FOREX_DATA_DIR=/path/to/data

# Set log level
export FOREX_LOG_LEVEL=INFO
```

## Running the System

### 1. Basic Usage
```bash
# Run complete data collection and dashboard update
python src/main.py
```

### 2. Individual Components
```bash
# Run only the scraper
python src/tradingeconomics_scraper.py

# Run only the dashboard updater
python src/dashboard_updater.py
```

### 3. Flask API Server
```bash
# Start the API server (if implemented)
python -m flask run --host=0.0.0.0 --port=5000
```

## Viewing Results

### 1. Dashboard
Open the generated dashboard in your web browser:
```bash
# Open the dashboard file
open dashboard.html  # macOS
start dashboard.html # Windows
xdg-open dashboard.html # Linux
```

### 2. Data Files
- **Dashboard Data**: `data/dashboard_data.json`
- **CSV Exports**: `data/exports/`
- **Economic Indicators**: `data/economic_indicators/`
- **Cache Files**: `data/cache/`

### 3. Logs
Monitor system activity through log files:
```bash
# View main application log
tail -f main.log

# View scraper activity
tail -f tradingeconomics_scraper.log

# View dashboard updates
tail -f dashboard_updater.log
```

## Scheduling (Optional)

### 1. Cron Job (Linux/macOS)
```bash
# Edit crontab
crontab -e

# Add entry to run every hour
0 * * * * /path/to/forex_env/bin/python /path/to/forex_system/src/main.py
```

### 2. Windows Task Scheduler
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (e.g., hourly)
4. Set action: Start a program
5. Program: `python.exe`
6. Arguments: `C:\path\to\forex_system\src\main.py`

### 3. Python Scheduler (Alternative)
```python
import schedule
import time
from src.main import main

# Schedule to run every hour
schedule.every().hour.do(main)

while True:
    schedule.run_pending()
    time.sleep(60)
```

## Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Error: ModuleNotFoundError
# Solution: Ensure virtual environment is activated and dependencies installed
pip install -r requirements.txt
```

#### 2. Permission Errors
```bash
# Error: Permission denied when creating directories
# Solution: Run with appropriate permissions or change directory
chmod 755 /path/to/forex_system
```

#### 3. Network Errors
```bash
# Error: Connection timeout or blocked requests
# Solution: Check internet connection and consider proxy settings
```

#### 4. Data Parsing Errors
```bash
# Error: BeautifulSoup parsing errors
# Solution: Website structure may have changed, check scraper logic
```

### Debug Mode
Enable debug logging for troubleshooting:
```python
# In any module, change logging level
logging.basicConfig(level=logging.DEBUG)
```

### Cache Issues
Clear cache if data seems stale:
```bash
# Remove all cache files
rm -rf data/cache/*

# Or remove specific country cache
rm data/cache/united-states_indicators.json
```

## Performance Optimization

### 1. Caching Configuration
Adjust cache settings in `tradingeconomics_scraper.py`:
```python
CACHE_DURATION = 3600  # 1 hour (adjust as needed)
CACHE_HISTORY_SIZE = 3  # Number of historical points
```

### 2. Request Delays
Modify scraping delays to balance speed vs. politeness:
```python
# In scrape_country_indicators()
time.sleep(random.uniform(1, 3))  # Adjust range as needed
```

### 3. Database Optimization
For large datasets, consider:
- Adding database indexes
- Using connection pooling
- Implementing data archiving

## Security Considerations

### 1. Web Scraping Ethics
- Respect robots.txt files
- Use reasonable request delays
- Monitor for rate limiting responses
- Consider using official APIs when available

### 2. Data Security
- Secure database connections
- Validate all input data
- Implement proper error handling
- Regular security updates

### 3. Network Security
- Use HTTPS when possible
- Implement request timeouts
- Monitor for suspicious activity
- Consider using VPN for scraping

## Monitoring and Maintenance

### 1. Log Rotation
Implement log rotation to prevent disk space issues:
```python
from logging.handlers import RotatingFileHandler

handler = RotatingFileHandler(
    'main.log', 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
```

### 2. Health Checks
Create monitoring scripts:
```python
def health_check():
    # Check if data is recent
    # Verify database connectivity
    # Test API endpoints
    # Monitor disk space
    pass
```

### 3. Data Validation
Regular data quality checks:
```python
def validate_data():
    # Check for missing indicators
    # Verify data ranges
    # Detect anomalies
    # Report data quality metrics
    pass
```

## Deployment Options

### 1. Local Development
- Run directly on development machine
- Use SQLite database
- Manual execution or simple scheduling

### 2. Server Deployment
- Deploy to VPS or cloud server
- Use MySQL/PostgreSQL database
- Implement proper logging and monitoring
- Set up automated backups

### 3. Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "src/main.py"]
```

### 4. Cloud Deployment
- AWS Lambda for serverless execution
- Google Cloud Functions
- Azure Functions
- Heroku for simple deployment

## Support and Maintenance

### 1. Regular Updates
- Monitor for website structure changes
- Update dependencies regularly
- Review and update indicator lists
- Optimize performance based on usage

### 2. Backup Strategy
- Regular database backups
- Configuration file backups
- Log file archiving
- Data export backups

### 3. Documentation Updates
- Keep API documentation current
- Update setup instructions
- Document configuration changes
- Maintain troubleshooting guides

## Advanced Configuration

### 1. Custom Indicators
Add new indicators by modifying `INDICATORS` dictionary:
```python
INDICATORS['custom_indicator'] = ['Custom Indicator Name']
```

### 2. Additional Currency Pairs
Extend `CURRENCY_PAIRS` configuration:
```python
CURRENCY_PAIRS['USD/SEK'] = {'base': 'united-states', 'quote': 'sweden'}
```

### 3. Custom Analysis Logic
Modify scoring algorithms in `generate_pair_analysis()`:
```python
# Adjust weights for different indicators
score += interest_rate_diff * 0.5  # Increase weight
```

### 4. Integration with External Systems
- REST API integration
- Database synchronization
- Email notifications
- Slack/Discord webhooks
- Trading platform integration
