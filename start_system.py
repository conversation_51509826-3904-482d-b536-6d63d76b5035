#!/usr/bin/env python3
"""
Complete startup script for Forex Analysis System
Handles database initialization and system startup
"""

import os
import sqlite3
import webbrowser
import time
import threading

def setup_directories():
    """Create necessary directories"""
    directories = [
        'data',
        'data/cache',
        'data/exports', 
        'data/economic_indicators',
        'instance'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Directory structure created")

def initialize_database():
    """Initialize the SQLite database"""
    db_path = os.path.join('instance', 'forex_data.db')
    
    print(f"🗄️ Initializing database at: {db_path}")
    
    try:
        # Create database connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create economic_indicator table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS economic_indicator (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                indicator_type VARCHAR(50) NOT NULL,
                currency VARCHAR(10) NOT NULL,
                value FLOAT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create dashboard_data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dashboard_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pair VARCHAR(10) NOT NULL,
                score FLOAT NOT NULL,
                signal VARCHAR(20) NOT NULL,
                confidence FLOAT NOT NULL,
                optimal_horizon INTEGER NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Commit and close
        conn.commit()
        conn.close()
        
        print("✅ Database initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def open_browser_delayed():
    """Open browser after a short delay"""
    time.sleep(3)  # Wait for server to start
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Opening browser at http://localhost:5000")
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print("   Please manually open: http://localhost:5000")

def main():
    """Main startup function"""
    print("🚀 FOREX ANALYSIS SYSTEM - COMPLETE STARTUP")
    print("=" * 50)
    
    # Step 1: Setup directories
    setup_directories()
    
    # Step 2: Initialize database
    if not initialize_database():
        print("❌ Cannot continue without database")
        input("Press Enter to exit...")
        return
    
    # Step 3: Start browser in background
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Step 4: Start Flask application
    try:
        print("📦 Loading Flask application...")
        
        # Add src to path
        import sys
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from app import app
        print("✅ Flask application loaded successfully")
        print("🌐 Starting web server...")
        print("📊 Dashboard will be available at: http://localhost:5000")
        print("🔄 The system will automatically open in your browser")
        print("\n" + "=" * 50)
        print("📝 INSTRUCTIONS:")
        print("   1. Wait for the browser to open")
        print("   2. Click 'Collect Data' to fetch economic indicators")
        print("   3. View analysis results on the dashboard")
        print("   4. Use the export features to save data")
        print("   5. Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start the Flask development server
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ Error importing Flask application: {e}")
        print("\n🔧 Trying to install missing packages...")
        
        import subprocess
        import sys
        
        packages = ['flask', 'flask-sqlalchemy', 'sqlalchemy', 'requests', 'beautifulsoup4']
        
        for package in packages:
            try:
                print(f"   Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            except Exception as install_error:
                print(f"   ⚠️ Failed to install {package}: {install_error}")
        
        print("\n🔄 Trying to start the application again...")
        try:
            from app import app
            app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
        except Exception as retry_error:
            print(f"❌ Still failed: {retry_error}")
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure all dependencies are installed")
        print("   2. Check that no other application is using port 5000")
        print("   3. Try running: python init_database.py")
        print("   4. Then try: python start_system.py")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
