#!/usr/bin/env python3
"""
Direct startup script that bypasses dependency checks
Use this if the regular run.py has import issues
"""

import os
import webbrowser
import time
import threading

def setup_directories():
    """Create necessary directories"""
    directories = [
        'data',
        'data/cache',
        'data/exports', 
        'data/economic_indicators',
        'instance'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Directory structure created")

def open_browser_delayed():
    """Open browser after a short delay"""
    time.sleep(3)  # Wait for server to start
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Opening browser at http://localhost:5000")
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print("   Please manually open: http://localhost:5000")

def main():
    """Main startup function - direct approach"""
    print("🚀 Starting Forex Analysis System (Direct Mode)")
    print("=" * 50)
    
    # Setup directories
    setup_directories()
    
    # Start browser in background
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Import and start the Flask app
    try:
        print("📦 Loading Flask application...")
        from app import app
        print("✅ Flask application loaded successfully")
        print("🌐 Starting web server...")
        print("📊 Dashboard will be available at: http://localhost:5000")
        print("🔄 The system will automatically open in your browser")
        print("\n" + "=" * 50)
        print("📝 INSTRUCTIONS:")
        print("   1. Wait for the browser to open")
        print("   2. Click 'Collect Data' to fetch economic indicators")
        print("   3. View analysis results on the dashboard")
        print("   4. Use the export features to save data")
        print("   5. Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start the Flask development server
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,  # Disable debug mode for cleaner output
            use_reloader=False  # Disable reloader to prevent double startup
        )
        
    except ImportError as e:
        print(f"❌ Error importing Flask application: {e}")
        print("\n🔧 Trying to install missing packages...")
        
        import subprocess
        import sys
        
        packages = ['flask', 'flask-sqlalchemy', 'sqlalchemy', 'requests', 'beautifulsoup4']
        
        for package in packages:
            try:
                print(f"   Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            except Exception as install_error:
                print(f"   ⚠️ Failed to install {package}: {install_error}")
        
        print("\n🔄 Trying to start the application again...")
        try:
            from app import app
            app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
        except Exception as retry_error:
            print(f"❌ Still failed: {retry_error}")
            print("\n📋 Manual installation required:")
            print("   pip install flask flask-sqlalchemy sqlalchemy requests beautifulsoup4")
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("\n🔍 Debug information:")
        print(f"   Python version: {sys.version}")
        print(f"   Working directory: {os.getcwd()}")
        print(f"   App.py exists: {os.path.exists('app.py')}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
