#!/usr/bin/env python3
"""
Quick system test to verify everything is working
"""

import os
import sys

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import flask
        print("✅ Flask imported successfully")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        import requests
        print("✅ Requests imported successfully")
    except ImportError as e:
        print(f"❌ Requests import failed: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup imported successfully")
    except ImportError as e:
        print(f"❌ BeautifulSoup import failed: {e}")
        return False
    
    try:
        import pandas
        print("✅ Pandas imported successfully")
    except ImportError as e:
        print(f"❌ Pandas import failed: {e}")
        return False
    
    return True

def test_directories():
    """Test that required directories exist or can be created"""
    print("\nTesting directory structure...")
    
    directories = [
        'data',
        'data/cache',
        'data/exports',
        'data/economic_indicators',
        'instance',
        'src',
        'src/templates',
        'src/models',
        'src/routes'
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory} exists")
        else:
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"✅ {directory} created")
            except Exception as e:
                print(f"❌ Failed to create {directory}: {e}")
                return False
    
    return True

def test_files():
    """Test that required files exist"""
    print("\nTesting required files...")
    
    required_files = [
        'app.py',
        'run.py',
        'requirements.txt',
        'src/main.py',
        'src/tradingeconomics_scraper.py',
        'src/dashboard_updater.py',
        'src/templates/dashboard.html',
        'src/models/economic_data.py',
        'src/routes/api.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            return False
    
    return True

def test_app_creation():
    """Test that the Flask app can be created"""
    print("\nTesting Flask app creation...")
    
    try:
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from app import create_app
        app = create_app()
        print("✅ Flask app created successfully")
        
        # Test that routes are registered
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        expected_routes = ['/', '/collect-data', '/status', '/dashboard-data']
        
        for route in expected_routes:
            if route in routes:
                print(f"✅ Route {route} registered")
            else:
                print(f"❌ Route {route} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 FOREX SYSTEM TEST SUITE")
    print("=" * 40)
    
    tests = [
        ("Import Test", test_imports),
        ("Directory Test", test_directories),
        ("File Test", test_files),
        ("App Creation Test", test_app_creation)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        if not test_func():
            all_passed = False
            print(f"❌ {test_name} FAILED")
        else:
            print(f"✅ {test_name} PASSED")
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ System is ready to run")
        print("🚀 You can now start the system with:")
        print("   - Windows: Double-click start_forex_system.bat")
        print("   - Mac/Linux: python run.py")
    else:
        print("❌ SOME TESTS FAILED")
        print("🔧 Please fix the issues above before running the system")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
