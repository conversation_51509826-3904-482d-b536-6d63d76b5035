@echo off
title Forex Analysis System Startup
color 0A

echo.
echo ========================================
echo    FOREX ANALYSIS SYSTEM STARTUP
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python found:
python --version

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo.
echo Installing/Updating dependencies...
echo This may take a few minutes...

REM Try to install dependencies
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo WARNING: Some dependencies failed to install
    echo Trying individual package installation...

    pip install Flask==3.1.0
    pip install Flask-SQLAlchemy==3.1.1
    pip install SQLAlchemy==2.0.40
    pip install requests==2.31.0
    pip install beautifulsoup4==4.12.2
    pip install lxml

    echo.
    echo Running quick test...
    python quick_test.py

    echo.
    echo If the test passed, the system should work.
    echo Some advanced features may be limited.
)

echo.
echo ========================================
echo    STARTING FOREX ANALYSIS SYSTEM
echo ========================================
echo.
echo The system will start in a few seconds...
echo Your browser will open automatically
echo.
echo To stop the system, press Ctrl+C
echo.

REM Start the application
python run.py

echo.
echo System has stopped.
pause
