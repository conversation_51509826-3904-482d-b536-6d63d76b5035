@echo off
title Forex Analysis System Startup
color 0A

echo.
echo ========================================
echo    FOREX ANALYSIS SYSTEM STARTUP
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo.
echo Installing/Updating dependencies...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo.
echo ========================================
echo    STARTING FOREX ANALYSIS SYSTEM
echo ========================================
echo.
echo The system will start in a few seconds...
echo Your browser will open automatically
echo.
echo To stop the system, press Ctrl+C
echo.

REM Start the application
python run.py

echo.
echo System has stopped.
pause
